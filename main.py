#!/usr/bin/env python3
"""
基金轮动策略主程序 - 518880和164701配对交易回测
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
from datetime import datetime
import logging

warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from backtest import (
    BacktestEngine, DataManager, 
    FundRotationStrategy,
    PerformanceAnalyzer, ChartGenerator, ReportGenerator
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_fund_data():
    """加载基金数据"""
    print("📊 加载基金数据...")
    
    # 创建数据管理器
    data_manager = DataManager(
        cache_dir="fund_data_cache",
        use_local_cache=True,
        auto_select_server=True
    )
    
    # 基金代码
    fund_codes = ['518880', '164701']
    start_date = '2022-01-01'
    end_date = '2025-07-16'
    frequency = '1min'  # 分钟线数据
    
    print(f"  基金代码: {fund_codes}")
    print(f"  回测区间: {start_date} 至 {end_date}")
    print(f"  数据频率: {frequency}")
    
    try:
        # 从pytdx加载数据
        fund_data = data_manager.load_pytdx(
            symbols=fund_codes,
            start_date=start_date,
            end_date=end_date,
            frequency=frequency,
            fields=['open', 'high', 'low', 'close', 'volume']
        )
        
        # 检查数据质量
        for code, data in fund_data.items():
            if data is None or data.empty:
                print(f"  ⚠️  {code} 数据为空，尝试使用模拟数据")
                fund_data[code] = create_mock_data(start_date, end_date, frequency)
            else:
                print(f"  ✅ {code} 数据加载成功: {len(data)} 条记录")
                print(f"    时间范围: {data.index.min()} 至 {data.index.max()}")
        
        return fund_data
        
    except Exception as e:
        print(f"  ⚠️  数据加载失败: {e}")
        print("  使用模拟数据进行演示...")

        # 创建模拟数据
        mock_data = {}
        for code in fund_codes:
            mock_data[code] = create_mock_data(start_date, end_date, frequency)
            print(f"    {code} 模拟数据: {len(mock_data[code])} 条记录")

        return mock_data


def create_mock_data(start_date, end_date, frequency):
    """创建模拟基金数据"""
    # 根据频率创建时间索引
    if frequency == '1min':
        # 创建交易时间（9:30-11:30, 13:00-15:00）
        dates = pd.date_range(start_date, end_date, freq='D')
        time_ranges = []

        for date in dates:
            # 跳过周末
            if date.weekday() >= 5:
                continue

            # 上午时段
            morning = pd.date_range(
                start=date.replace(hour=9, minute=30),
                end=date.replace(hour=11, minute=30),
                freq='1min'
            )
            # 下午时段
            afternoon = pd.date_range(
                start=date.replace(hour=13, minute=0),
                end=date.replace(hour=15, minute=0),
                freq='1min'
            )
            time_ranges.extend(morning)
            time_ranges.extend(afternoon)

        time_index = pd.DatetimeIndex(time_ranges)
    else:
        time_index = pd.date_range(start_date, end_date, freq=frequency)

    # 生成价格数据
    np.random.seed(42)
    returns = np.random.normal(0, 0.001, len(time_index))  # 分钟收益率
    prices = 100 * np.exp(np.cumsum(returns))

    # 创建OHLCV数据
    data = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.0005, len(time_index))),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.001, len(time_index)))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.001, len(time_index)))),
        'close': prices,
        'volume': np.random.randint(100000, 1000000, len(time_index))
    }, index=time_index)

    return data


def run_fund_rotation_backtest(fund_data):
    """运行基金轮动策略回测"""
    print("\n📈 运行基金轮动策略回测...")
    
    try:
        # 创建基金轮动策略
        strategy = FundRotationStrategy(
            fund_pair=('518880', '164701'),
            window=120,  # 120分钟 = 1天（考虑分钟线数据）
            std_dev_mult=1.0,
            commission_rate=0.00005,  # 佣金率
            slippage_rate=0.001,     # 0.1%滑点
            position_size=1.0,
            name="518880-164701基金轮动策略"
        )
        
        # 设置数据（基金轮动策略需要字典格式的数据）
        strategy.set_fund_data(fund_data)
        
        # 配置交易规则
        commission_config = {
            '518880': {'rate': 0.00005, 'min': 0.01},  # ETF基金
            '164701': {'rate': 0.00005, 'min': 5.0}   # 指数基金
        }
        
        # 创建回测引擎
        engine = BacktestEngine(
            initial_capital=1000000,  # 100万初始资金
            commission_rate=0.00005,
            min_trade_unit=100,
            symbol_commission_config=commission_config
        )
        
        # 使用518880作为主要数据进行回测
        main_data = fund_data['518880']
        
        # 运行回测
        print("  执行回测计算...")

        # 调试：检查策略的信号和仓位
        signals = strategy.generate_signals()
        positions = strategy.calculate_positions(signals)
        position_changes = positions.diff().fillna(positions.iloc[0])
        trade_points = position_changes[position_changes != 0]

        print(f"  调试信息:")
        print(f"    信号统计: {signals.value_counts().to_dict()}")
        print(f"    仓位统计: {positions.value_counts().to_dict()}")
        print(f"    仓位变化点数量: {len(trade_points)}")
        if len(trade_points) > 0:
            print(f"    仓位变化统计: {trade_points.value_counts().to_dict()}")
            print(f"    前5个仓位变化: {trade_points.head().to_dict()}")

        # 直接运行策略查看交易生成
        positions_direct, trades_direct = strategy.run()
        print(f"    直接运行策略结果:")
        print(f"      交易数量: {len(trades_direct)}")
        if len(trades_direct) > 0:
            print(f"      前3笔交易: {trades_direct.head(3).to_dict('records')}")

        results = engine.run_backtest(strategy, main_data)

        # 获取策略交易摘要
        summary = strategy.get_trading_summary()
        
        print("  回测完成!")
        print(f"    策略信号统计:")
        print(f"      总信号数: {summary.get('total_signals', 0)}")
        print(f"      买入518880信号: {summary.get('buy_fund1_signals', 0)}")
        print(f"      买入164701信号: {summary.get('buy_fund2_signals', 0)}")
        print(f"      信号比例: {summary.get('signal_ratio', 0):.2%}")
        
        # 显示基本性能指标
        metrics = results['performance_metrics']
        print(f"    基本性能指标:")
        print(f"      总收益率: {metrics['total_return']:.2%}")
        print(f"      年化收益率: {metrics['annualized_return']:.2%}")
        print(f"      夏普比率: {metrics['sharpe_ratio']:.4f}")
        print(f"      最大回撤: {metrics['max_drawdown']:.2%}")
        print(f"      交易次数: {len(results['trades'])}")
        
        return results, strategy
        
    except Exception as e:
        print(f"  ❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def generate_comprehensive_analysis(results, strategy, fund_data):
    """生成综合分析和可视化"""
    print("\n📊 生成综合分析和可视化...")
    
    if results is None:
        print("  跳过分析（回测失败）")
        return
    
    # 创建输出目录
    output_dir = "fund_rotation_analysis"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 生成标准回测报告
    print("  生成标准回测报告...")
    report_gen = ReportGenerator(output_dir=output_dir)
    report_paths = report_gen.generate_full_report(
        results,
        strategy_name="518880-164701基金轮动策略",
        report_title="基金轮动策略回测分析报告"
    )
    
    # 2. 生成策略特定分析
    print("  生成策略特定分析...")
    generate_strategy_specific_analysis(results, strategy, fund_data, output_dir)
    
    # 3. 生成基金对比分析
    print("  生成基金对比分析...")
    generate_fund_comparison_analysis(fund_data, output_dir)
    
    # 4. 生成交易分析
    print("  生成交易分析...")
    generate_trading_analysis(results, output_dir)
    
    print(f"  ✅ 所有分析完成，结果保存在: {output_dir}/")
    print(f"    主要文件:")
    for report_type, path in report_paths.items():
        print(f"      {report_type}: {path}")


def generate_strategy_specific_analysis(results, strategy, fund_data, output_dir):
    """生成策略特定分析"""
    
    # 获取策略信号数据
    signals_df = strategy.get_signals_dataframe()
    
    if signals_df is None or signals_df.empty:
        print("    无策略信号数据")
        return
    
    # 创建策略分析图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 价格比率和交易区间
    ax1.plot(signals_df.index, signals_df['price_ratio'], label='价格比率', linewidth=1)
    ax1.plot(signals_df.index, signals_df['upper_bound'], label='上边界', linestyle='--', alpha=0.7)
    ax1.plot(signals_df.index, signals_df['lower_bound'], label='下边界', linestyle='--', alpha=0.7)
    ax1.fill_between(signals_df.index, signals_df['upper_bound'], signals_df['lower_bound'], alpha=0.2)
    ax1.set_title('价格比率与交易区间', fontsize=14)
    ax1.set_ylabel('价格比率')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Beta系数变化
    ax2.plot(signals_df.index, signals_df['beta'], label='滚动Beta', color='orange')
    ax2.set_title('滚动Beta系数', fontsize=14)
    ax2.set_ylabel('Beta值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 交易信号分布
    signal_counts = signals_df['trade_signal'].value_counts()
    signal_labels = {-1: '买入164701', 0: '无交易', 1: '买入518880'}
    labels = [signal_labels.get(k, str(k)) for k in signal_counts.index]
    ax3.pie(signal_counts.values, labels=labels, autopct='%1.1f%%')
    ax3.set_title('交易信号分布', fontsize=14)
    
    # 4. 基金价格走势对比
    ax4.plot(fund_data['518880'].index, fund_data['518880']['open'], 
             label='518880 (开盘价)', alpha=0.8)
    ax4.plot(fund_data['164701'].index, fund_data['164701']['open'], 
             label='164701 (开盘价)', alpha=0.8)
    ax4.set_title('基金价格走势对比', fontsize=14)
    ax4.set_ylabel('价格')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/strategy_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存策略信号数据
    signals_df.to_csv(f'{output_dir}/strategy_signals.csv', encoding='utf-8-sig')


def generate_fund_comparison_analysis(fund_data, output_dir):
    """生成基金对比分析"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 价格标准化对比
    fund1_norm = fund_data['518880']['open'] / fund_data['518880']['open'].iloc[0]
    fund2_norm = fund_data['164701']['open'] / fund_data['164701']['open'].iloc[0]
    
    ax1.plot(fund1_norm.index, fund1_norm, label='518880 (标准化)', linewidth=1.5)
    ax1.plot(fund2_norm.index, fund2_norm, label='164701 (标准化)', linewidth=1.5)
    ax1.set_title('基金价格标准化对比', fontsize=14)
    ax1.set_ylabel('标准化价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 收益率分布对比
    returns1 = fund_data['518880']['open'].pct_change().dropna()
    returns2 = fund_data['164701']['open'].pct_change().dropna()
    
    ax2.hist(returns1, bins=50, alpha=0.7, label='518880', density=True)
    ax2.hist(returns2, bins=50, alpha=0.7, label='164701', density=True)
    ax2.set_title('收益率分布对比', fontsize=14)
    ax2.set_xlabel('收益率')
    ax2.set_ylabel('密度')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 滚动相关性
    window = 1440  # 1天窗口
    correlation = returns1.rolling(window=window).corr(returns2)
    
    ax3.plot(correlation.index, correlation, label='滚动相关性', color='green')
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax3.set_title(f'{window}分钟滚动相关性', fontsize=14)
    ax3.set_ylabel('相关系数')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 成交量对比
    ax4.plot(fund_data['518880'].index, fund_data['518880']['volume'], 
             label='518880 成交量', alpha=0.7)
    ax4.plot(fund_data['164701'].index, fund_data['164701']['volume'], 
             label='164701 成交量', alpha=0.7)
    ax4.set_title('成交量对比', fontsize=14)
    ax4.set_ylabel('成交量')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/fund_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存统计数据
    stats_data = {
        '指标': ['平均收益率', '收益率标准差', '夏普比率', '最大值', '最小值'],
        '518880': [
            returns1.mean(),
            returns1.std(),
            returns1.mean() / returns1.std() if returns1.std() != 0 else 0,
            fund_data['518880']['open'].max(),
            fund_data['518880']['open'].min()
        ],
        '164701': [
            returns2.mean(),
            returns2.std(),
            returns2.mean() / returns2.std() if returns2.std() != 0 else 0,
            fund_data['164701']['open'].max(),
            fund_data['164701']['open'].min()
        ]
    }
    
    stats_df = pd.DataFrame(stats_data)
    stats_df.to_csv(f'{output_dir}/fund_statistics.csv', index=False, encoding='utf-8-sig')


def generate_trading_analysis(results, output_dir):
    """生成交易分析"""
    
    trades = results['trades']
    equity_curve = results['equity_curve']
    
    if trades.empty:
        print("    无交易记录")
        return
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 权益曲线和回撤
    ax1.plot(equity_curve.index, equity_curve['equity'], label='权益曲线', linewidth=2)
    ax1_twin = ax1.twinx()
    ax1_twin.fill_between(equity_curve.index, equity_curve['drawdown'], 0, 
                         color='red', alpha=0.3, label='回撤')
    ax1.set_title('权益曲线与回撤', fontsize=14)
    ax1.set_ylabel('权益价值')
    ax1_twin.set_ylabel('回撤比例')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 2. 交易盈亏分布
    trades['pnl'] = trades.apply(lambda x: x['value'] if x['type'] == 'sell' else -x['value'], axis=1)
    ax2.hist(trades['pnl'], bins=20, alpha=0.7, edgecolor='black')
    ax2.axvline(x=0, color='red', linestyle='--')
    ax2.set_title('交易盈亏分布', fontsize=14)
    ax2.set_xlabel('盈亏金额')
    ax2.set_ylabel('频次')
    ax2.grid(True, alpha=0.3)
    
    # 3. 月度收益热图
    monthly_returns = equity_curve['returns'].resample('M').apply(lambda x: (1 + x).prod() - 1)
    monthly_df = monthly_returns.to_frame('returns')
    monthly_df['year'] = monthly_df.index.year
    monthly_df['month'] = monthly_df.index.month
    
    pivot_table = monthly_df.pivot(index='year', columns='month', values='returns')
    
    import seaborn as sns
    sns.heatmap(pivot_table, annot=True, fmt='.2%', cmap='RdYlGn', center=0, ax=ax3)
    ax3.set_title('月度收益热图', fontsize=14)
    
    # 4. 累计交易次数
    trades_cumsum = trades.groupby(trades['datetime'].dt.date).size().cumsum()
    ax4.plot(trades_cumsum.index, trades_cumsum.values, marker='o', markersize=3)
    ax4.set_title('累计交易次数', fontsize=14)
    ax4.set_ylabel('累计交易次数')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/trading_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存交易详细数据
    trades.to_csv(f'{output_dir}/trades_detail.csv', index=False, encoding='utf-8-sig')


def main():
    """主函数"""
    print("🚀 基金轮动策略回测分析")
    print("=" * 60)
    print("配对基金: 518880 vs 164701")
    print("回测区间: 2022-01-01 至 2025-07-16")
    print("数据频率: 分钟线")
    print("使用价格: 开盘价")
    print("=" * 60)
    
    try:
        # 1. 加载基金数据
        fund_data = load_fund_data()
        
        # 2. 运行基金轮动策略回测
        results, strategy = run_fund_rotation_backtest(fund_data)
        
        # 3. 生成综合分析和可视化
        generate_comprehensive_analysis(results, strategy, fund_data)
        
        print("\n🎉 分析完成!")
        print("=" * 60)
        print("输出文件:")
        print("  📁 主目录: fund_rotation_analysis/")
        print("  📊 HTML报告: fund_rotation_analysis/*_report.html")
        print("  📈 策略分析: fund_rotation_analysis/strategy_analysis.png")
        print("  📊 基金对比: fund_rotation_analysis/fund_comparison.png")
        print("  💼 交易分析: fund_rotation_analysis/trading_analysis.png")
        print("  📋 数据文件: fund_rotation_analysis/*.csv")
        
        print("\n💡 建议:")
        print("  - 查看HTML报告获取完整分析")
        print("  - 检查strategy_signals.csv了解交易信号")
        print("  - 分析fund_statistics.csv比较基金特性")
        print("  - 如需实盘交易，请谨慎评估风险")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
