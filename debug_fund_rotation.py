#!/usr/bin/env python3
"""
调试基金轮动策略的交易生成问题
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from backtest.strategies.fund_rotation import FundRotationStrategy

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range('2025-01-01', periods=100, freq='1min')
    
    # 创建两个基金的价格数据
    fund1_prices = 100 + np.cumsum(np.random.normal(0, 0.1, 100))
    fund2_prices = 20 + np.cumsum(np.random.normal(0, 0.02, 100))
    
    fund1_data = pd.DataFrame({
        'open': fund1_prices,
        'high': fund1_prices * 1.01,
        'low': fund1_prices * 0.99,
        'close': fund1_prices,
        'volume': np.random.randint(1000, 10000, 100)
    }, index=dates)
    
    fund2_data = pd.DataFrame({
        'open': fund2_prices,
        'high': fund2_prices * 1.01,
        'low': fund2_prices * 0.99,
        'close': fund2_prices,
        'volume': np.random.randint(1000, 10000, 100)
    }, index=dates)
    
    return {'fund1': fund1_data, 'fund2': fund2_data}

def test_fund_rotation():
    """测试基金轮动策略"""
    print("🔍 调试基金轮动策略...")
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 创建策略
    strategy = FundRotationStrategy(
        fund_pair=('fund1', 'fund2'),
        window=20,
        std_dev_mult=1.0,
        commission_rate=0.001,
        slippage_rate=0.001,
        position_size=1.0,
        name="测试基金轮动策略"
    )
    
    # 设置数据
    strategy.set_fund_data(test_data)
    
    # 生成信号
    signals = strategy.generate_signals()
    print(f"信号统计: {signals.value_counts().to_dict()}")
    
    # 计算仓位
    positions = strategy.calculate_positions(signals)
    print(f"仓位统计: {positions.value_counts().to_dict()}")
    
    # 计算仓位变化
    position_changes = positions.diff().fillna(positions.iloc[0])
    trade_points = position_changes[position_changes != 0]
    print(f"仓位变化点数量: {len(trade_points)}")
    print(f"仓位变化统计: {trade_points.value_counts().to_dict()}")
    
    # 运行策略
    positions_result, trades_result = strategy.run()
    print(f"交易数量: {len(trades_result)}")
    
    if len(trades_result) > 0:
        print("前5笔交易:")
        print(trades_result.head())
    else:
        print("❌ 没有生成交易")
        
        # 调试信息
        print("\n调试信息:")
        print(f"signals前10个值: {signals.head(10).tolist()}")
        print(f"positions前10个值: {positions.head(10).tolist()}")
        print(f"position_changes前10个值: {position_changes.head(10).tolist()}")
        
        # 检查第一个非零信号
        first_signal_idx = signals[signals != 0].index[0] if len(signals[signals != 0]) > 0 else None
        if first_signal_idx is not None:
            print(f"第一个非零信号时间: {first_signal_idx}")
            print(f"第一个非零信号值: {signals[first_signal_idx]}")
            print(f"对应仓位: {positions[first_signal_idx]}")
            print(f"对应仓位变化: {position_changes[first_signal_idx]}")

if __name__ == "__main__":
    test_fund_rotation()
