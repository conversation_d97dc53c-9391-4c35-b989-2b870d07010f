#coding: utf-8
# see https://github.com/rainx/pytdx/issues/38 IP寻优的简单办法
# by yutianst

import datetime
from pytdx.hq import TdxHq_API
from pytdx.exhq import TdxExHq_API

stock_ip = [{'ip': '*************', 'port': 7711, 'name': '北京行情主站1'},
 {'ip': '*************', 'port': 7709, 'name': '深圳行情主站'},
 {'ip': '*************', 'port': 7711, 'name': '深圳行情主站'},
 {'ip': '*************', 'port': 7711, 'name': '上海行情主站'},
 {'ip': '***************', 'port': 7711, 'name': '移动行情主站'},
 {'ip': '***************', 'port': 443, 'name': '广州行情主站'},
 {'ip': '***************', 'port': 80, 'name': '广州行情主站'},
 {'ip': '**************', 'port': 7711, 'name': '杭州行情主站'},
 {'ip': '***************', 'port': 7711, 'name': '北京行情主站2'},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '************', 'port': 7709},
 {'ip': '************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '***************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '***************', 'port': 7709},
 {'ip': '************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '**********', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************0', 'port': 7709},
 {'ip': '*************1', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '***************', 'port': 7709},
 {'ip': '************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '***********', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '*************', 'port': 7709},
 {'ip': '**************', 'port': 7721},
 {'ip': '*************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': '**************', 'port': 7709},
 {'ip': 'hq.cjis.cn', 'port': 7709},
 {'ip': 'hq1.daton.com.cn', 'port': 7709},
 {'ip': 'jstdx.gtjas.com', 'port': 7709},
 {'ip': 'shtdx.gtjas.com', 'port': 7709},
 {'ip': 'sztdx.gtjas.com', 'port': 7709},
 {'ip': '***************', 'port': 7721},
 {'ip': '**************', 'port': 7721}]

future_ip = [{'ip': '*************', 'port': 7727, 'name': '扩展市场上海双线'},
 {'ip': '*************', 'port': 7727, 'name': '扩展市场深圳双线1'},
 {'ip': '**************', 'port': 7727, 'name': '扩展市场深圳主站'},
 {'ip': '************', 'port': 7727, 'name': '扩展市场武汉主站1'},
 {'ip': '***********', 'port': 7727, 'name': '扩展市场深圳双线2'},
 {'ip': '*************', 'port': 7721},
 {'ip': '*************', 'port': 443, 'name': '扩展市场武汉主站2'},
 {'ip': '*************', 'port': 7727, 'name': '扩展市场北京主站'},
 {'ip': '*************', 'port': 7727, 'name': '扩展市场武汉主站3'},
 {'ip': '**************', 'port': 7727, 'name': '扩展市场上海主站1'},
 {'ip': '**************', 'port': 7727, 'name': '扩展市场上海主站2'},
 {'ip': '**************', 'port': 7721, 'name': '扩展市场深圳主站'},
 {'ip': '*************', 'port': 7727, 'name': '扩展市场深圳双线3'}]

def ping(ip, port=7709, type_='stock'):
    api = TdxHq_API()
    apix = TdxExHq_API()
    __time1 = datetime.datetime.now()
    try:
        if type_ in ['stock']:
            with api.connect(ip, port, time_out=0.7):
                res = api.get_security_list(0, 1)
                #print(len(res))
                if res is not None:
                    if len(res) > 800:
                        print('GOOD RESPONSE {}'.format(ip))
                        return datetime.datetime.now() - __time1
                    else:
                        print('BAD RESPONSE {}'.format(ip))
                        return datetime.timedelta(9, 9, 0)
                else:

                    print('BAD RESPONSE {}'.format(ip))
                    return datetime.timedelta(9, 9, 0)
        elif type_ in ['future']:
            with apix.connect(ip, port, time_out=0.7):
                res = apix.get_instrument_count()
                if res is not None:
                    if res > 20000:
                        print('GOOD RESPONSE {}'.format(ip))
                        return datetime.datetime.now() - __time1
                    else:
                        print('️Bad FUTUREIP REPSONSE {}'.format(ip))
                        return datetime.timedelta(9, 9, 0)
                else:
                    print('️Bad FUTUREIP REPSONSE {}'.format(ip))
                    return datetime.timedelta(9, 9, 0)
    except Exception as e:
        if isinstance(e, TypeError):
            print(e)
            print('Tushare内置的pytdx版本和最新的pytdx 版本不同, 请重新安装pytdx以解决此问题')
            print('pip uninstall pytdx')
            print('pip install pytdx')

        else:
            print('BAD RESPONSE {}'.format(ip))
        return datetime.timedelta(9, 9, 0)



def select_best_ip(_type='stock'):
    """目前这里给的是单线程的选优, 如果需要多进程的选优/ 最优ip缓存 可以参考
    https://github.com/QUANTAXIS/QUANTAXIS/blob/master/QUANTAXIS/QAFetch/QATdx.py#L106


    Keyword Arguments:
        _type {str} -- [description] (default: {'stock'})
    
    Returns:
        [type] -- [description]
    """
    best_ip = {
        'stock': {
            'ip': None, 'port': None
        },
        'future': {
            'ip': None, 'port': None
        }
    }
    ip_list = stock_ip if _type== 'stock' else future_ip
    
    data = [ping(x['ip'], x['port'], _type) for x in ip_list]
    results = []
    for i in range(len(data)):
        # 删除ping不通的数据
        if data[i] < datetime.timedelta(0, 9, 0):
            results.append((data[i], ip_list[i]))
    # 按照ping值从小大大排序
    results = [x[1] for x in sorted(results, key=lambda x: x[0])]
    
    return results[0]

if __name__ == '__main__':
    ip = select_best_ip('stock')
    print(ip)
    ip = select_best_ip('future')
    print(ip)