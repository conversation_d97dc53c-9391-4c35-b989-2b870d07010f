import numpy as np

from statsmodels.tools.tools import Bunch

llf = np.array([-242.89663276735])

nobs = np.array([202])

k = np.array([3])

k_exog = np.array([1])

sigma = np.array([.8053519404535])

chi2 = np.array([15723.381396967])

df_model = np.array([2])

k_ar = np.array([1])

k_ma = np.array([1])

params = np.array([
    .99479180506163,
    -.84461527652809,
    .64859174799221])

cov_params = np.array([
    .00008904968254,
    -.00023560410507,
    .00012795903324,
    -.00023560410507,
    .00131628534915,
    -.00022462340695,
    .00012795903324,
    -.00022462340695,
    .0005651128627]).reshape(3, 3)

xb = np.array([
    0,
    0,
    .02869686298072,
    .05651443824172,
    .0503994859755,
    .06887971609831,
    .05940540507436,
    .08067328482866,
    .08167565613985,
    .06429278105497,
    .07087650150061,
    .06886467337608,
    .06716959923506,
    .08230647444725,
    .07099691033363,
    .08401278406382,
    .07996553182602,
    .07354256510735,
    .09366323798895,
    .08811800926924,
    .10296355187893,
    .08846370875835,
    .0852297320962,
    .08700425922871,
    .09751411527395,
    .09737934917212,
    .11228405684233,
    .1053489819169,
    .12352022528648,
    .16439816355705,
    .1643835157156,
    .19891132414341,
    .17551273107529,
    .17827558517456,
    .19562774896622,
    .21028305590153,
    .23767858743668,
    .24580039083958,
    .28269505500793,
    .29883882403374,
    .31247469782829,
    .35402658581734,
    .37410452961922,
    .39106267690659,
    .42040377855301,
    .44518512487411,
    .43608102202415,
    .44340893626213,
    .44959822297096,
    .40977239608765,
    .42118826508522,
    .40079545974731,
    .38357082009315,
    .36902260780334,
    .35673499107361,
    .36137464642525,
    .38031083345413,
    .47139286994934,
    .47323387861252,
    .60994738340378,
    .69538277387619,
    .7825602889061,
    .84117436408997,
    .9657689332962,
    1.0109325647354,
    .95897275209427,
    .96013957262039,
    .9461076259613,
    .9342554807663,
    .83413934707642,
    .83968591690063,
    .84437066316605,
    .83330947160721,
    .8990553021431,
    .87949693202972,
    .86297762393951,
    .89407861232758,
    .93536442518234,
    1.0303052663803,
    1.1104937791824,
    1.1481873989105,
    1.2851470708847,
    1.4458787441254,
    1.5515991449356,
    1.7309991121292,
    1.8975404500961,
    1.8579913377762,
    1.8846583366394,
    1.9672524929047,
    1.9469071626663,
    2.0048115253448,
    1.9786299467087,
    1.8213576078415,
    1.6284521818161,
    1.7508568763733,
    1.5689061880112,
    1.2950873374939,
    1.2290096282959,
    1.1882168054581,
    1.1537625789642,
    1.1697143316269,
    1.1681711673737,
    1.106795668602,
    1.0849931240082,
    1.006507396698,
    1.0453414916992,
    .98803448677063,
    .95465070009232,
    1.0165599584579,
    .67838954925537,
    .69311982393265,
    .69054269790649,
    .76345545053482,
    .84005492925644,
    .87471830844879,
    .91901183128357,
    .92638796567917,
    .96265280246735,
    1.0083012580872,
    1.0618740320206,
    1.0921038389206,
    1.2077431678772,
    1.2303256988525,
    1.174311041832,
    1.3072115182877,
    1.314337015152,
    1.3503924608231,
    1.5760731697083,
    1.5264053344727,
    1.34929728508,
    1.304829955101,
    1.2522557973862,
    1.222869515419,
    1.198047041893,
    1.1770839691162,
    1.1743944883347,
    1.1571066379547,
    1.1274864673615,
    1.0574153661728,
    1.058304309845,
    .99898308515549,
    .9789143204689,
    1.0070173740387,
    1.000718832016,
    1.0104174613953,
    1.0486439466476,
    1.0058424472809,
    .98470783233643,
    1.0119106769562,
    1.0649236440659,
    1.0346088409424,
    1.0540577173233,
    1.0704846382141,
    .97923594713211,
    .90216588973999,
    .9271782040596,
    .85819715261459,
    .75488126277924,
    .78776079416275,
    .77047789096832,
    .77089905738831,
    .8313245177269,
    .82229107618332,
    .90476810932159,
    .94439232349396,
    1.0379292964935,
    1.1469690799713,
    1.1489590406418,
    1.2257302999496,
    1.1554099321365,
    1.1260533332825,
    .9811190366745,
    .8436843752861,
    .95287209749222,
    .90993344783783,
    .94875508546829,
    1.0115815401077,
    .94450175762177,
    .87282890081406,
    .91741597652435,
    .98511207103729,
    .9972335100174,
    1.0975805521011,
    1.1823329925537,
    1.1487929821014,
    1.270641207695,
    1.2083609104156,
    1.696394443512,
    1.4628355503082,
    1.4307631254196,
    1.5087975263596,
    1.1542117595673,
    1.2262620925903,
    1.3880327939987,
    1.3853038549423,
    1.4396153688431,
    1.7208145856857,
    1.678991317749,
    2.110867023468,
    1.524417757988,
    .57946246862411,
    .56406193971634,
    .74643105268478])

y = np.array([
    np.nan,
    28.979999542236,
    29.178695678711,
    29.40651512146,
    29.420400619507,
    29.608880996704,
    29.609405517578,
    29.830673217773,
    29.921676635742,
    29.874292373657,
    29.990877151489,
    30.048864364624,
    30.10717010498,
    30.292304992676,
    30.290996551514,
    30.464012145996,
    30.519966125488,
    30.553541183472,
    30.783664703369,
    30.838117599487,
    31.042964935303,
    31.038463592529,
    31.105230331421,
    31.207004547119,
    31.377513885498,
    31.477378845215,
    31.692283630371,
    31.755348205566,
    32.003520965576,
    32.444396972656,
    32.61438369751,
    33.048908233643,
    33.07551574707,
    33.278274536133,
    33.595630645752,
    33.91028213501,
    34.337677001953,
    34.645801544189,
    35.182697296143,
    35.598838806152,
    36.012474060059,
    36.654026031494,
    37.174102783203,
    37.691062927246,
    38.320404052734,
    38.94518661499,
    39.336082458496,
    39.843410491943,
    40.349597930908,
    40.509769439697,
    41.021186828613,
    41.300796508789,
    41.583572387695,
    41.869022369385,
    42.156734466553,
    42.561374664307,
    43.080310821533,
    44.171394348145,
    44.673233032227,
    46.209945678711,
    47.495380401611,
    48.882556915283,
    50.141174316406,
    51.965770721436,
    53.310932159424,
    53.958972930908,
    54.960140228271,
    55.84610748291,
    56.734252929688,
    56.934139251709,
    57.839687347412,
    58.744373321533,
    59.533309936523,
    60.899055480957,
    61.679496765137,
    62.46297454834,
    63.594078063965,
    64.83536529541,
    66.530303955078,
    68.210494995117,
    69.64818572998,
    71.885147094727,
    74.445877075195,
    76.751594543457,
    79.731002807617,
    82.797538757324,
    84.457992553711,
    86.584655761719,
    89.167251586914,
    91.046905517578,
    93.504814147949,
    95.378631591797,
    96.22135925293,
    96.628448486328,
    99.250854492188,
    99.668907165527,
    99.195091247559,
    100.0290145874,
    100.98822021484,
    101.95376586914,
    103.26971435547,
    104.46817779541,
    105.20679473877,
    106.1849899292,
    106.70650482178,
    108.0453414917,
    108.68803405762,
    109.45465087891,
    110.91656494141,
    109.37838745117,
    110.19312286377,
    110.89054107666,
    112.16345977783,
    113.54005432129,
    114.67472076416,
    115.91901397705,
    116.92639160156,
    118.16265106201,
    119.50830078125,
    120.96187591553,
    122.29209899902,
    124.30773925781,
    125.7303237915,
    126.57431030273,
    128.8072052002,
    130.21432495117,
    131.85038757324,
    134.97607421875,
    136.22640991211,
    136.44931030273,
    137.50482177734,
    138.45225524902,
    139.5228729248,
    140.59803771973,
    141.67707824707,
    142.87438964844,
    143.95710754395,
    144.92749023438,
    145.55741882324,
    146.65830993652,
    147.29898071289,
    148.17890930176,
    149.40701293945,
    150.40071105957,
    151.51042175293,
    152.84864807129,
    153.60585021973,
    154.48471069336,
    155.7119140625,
    157.16493225098,
    158.03460693359,
    159.25405883789,
    160.47047424316,
    160.87922668457,
    161.30215454102,
    162.42718505859,
    162.85820007324,
    162.95487976074,
    163.98776245117,
    164.67047119141,
    165.47090148926,
    166.73132324219,
    167.52229309082,
    169.00477600098,
    170.24440002441,
    171.93792724609,
    173.84696960449,
    175.04895019531,
    176.82572937012,
    177.55540466309,
    178.52604675293,
    178.58113098145,
    178.54368591309,
    180.25286865234,
    180.90992736816,
    182.14875793457,
    183.61158752441,
    184.14450073242,
    184.5728302002,
    185.81741333008,
    187.28511047363,
    188.39723205566,
    190.19758605957,
    191.98233032227,
    192.94879150391,
    195.07064819336,
    195.90835571289,
    200.89639282227,
    200.86282348633,
    202.13075256348,
    204.20880126953,
    203.05419921875,
    204.80026245117,
    207.3080291748,
    208.72329711914,
    210.57261657715,
    214.21580505371,
    215.67597961426,
    220.72087097168,
    218.41342163086,
    212.75346374512,
    213.23506164551,
    215.21542358398])

resid = np.array([
    np.nan,
    .17000007629395,
    .17130389809608,
    -.03651398047805,
    .11960058659315,
    -.05888139456511,
    .14059536159039,
    .00932686589658,
    -.11167634278536,
    .04570783302188,
    -.0108770346269,
    -.00886330008507,
    .10282856971025,
    -.07230624556541,
    .08900293707848,
    -.0240114107728,
    -.03996651992202,
    .13645842671394,
    -.03366377204657,
    .10188252478838,
    -.09296332299709,
    -.01846401393414,
    .01477065030485,
    .0729955881834,
    .00248436117545,
    .10262141376734,
    -.04228436201811,
    .12465056031942,
    .27647939324379,
    .00560382334515,
    .23561419546604,
    -.1489082723856,
    .02448422275484,
    .12172746658325,
    .10437148809433,
    .18971465528011,
    .06232447177172,
    .25419962406158,
    .11730266362429,
    .10116269439459,
    .2875237762928,
    .14597341418266,
    .12589547038078,
    .20893961191177,
    .17959471046925,
    -.04518361017108,
    .06391899287701,
    .05659105628729,
    -.24960128962994,
    .09022761881351,
    -.12118522822857,
    -.10079623758793,
    -.08357158303261,
    -.06902338564396,
    .04326653853059,
    .13862533867359,
    .61968916654587,
    .02860714122653,
    .92676383256912,
    .59005337953568,
    .60461646318436,
    .41744044423103,
    .85882639884949,
    .33423033356667,
    -.31093180179596,
    .04102724045515,
    -.06013804674149,
    -.04610994458199,
    -.63425624370575,
    .06586220860481,
    .06031560897827,
    -.04437142238021,
    .46668976545334,
    -.09905604273081,
    -.07949769496918,
    .23702463507652,
    .30592212080956,
    .66463404893875,
    .56969320774078,
    .28950771689415,
    .95181107521057,
    1.1148544549942,
    .75411820411682,
    1.2484039068222,
    1.1690024137497,
    -.1975435167551,
    .24200716614723,
    .6153416633606,
    -.06725100427866,
    .45309436321259,
    -.10480991750956,
    -.97863000631332,
    -1.2213591337204,
    .8715478181839,
    -1.1508584022522,
    -1.7689031362534,
    -.39508575201035,
    -.22900961339474,
    -.18821682035923,
    .14623281359673,
    .03029025532305,
    -.36817568540573,
    -.10679569840431,
    -.48499462008476,
    .29349562525749,
    -.34534454345703,
    -.18803144991398,
    .44535079598427,
    -2.2165644168854,
    .12161350995302,
    .00687709869817,
    .50946187973022,
    .53653997182846,
    .25995117425919,
    .32527860999107,
    .08098815381527,
    .27360898256302,
    .33735024929047,
    .39170032739639,
    .23812144994736,
    .80789774656296,
    .19225835800171,
    -.33032417297363,
    .92568749189377,
    .09278241544962,
    .28566908836365,
    1.5496014356613,
    -.27607008814812,
    -1.1263961791992,
    -.24930645525455,
    -.30482992529869,
    -.15224970877171,
    -.12287864089012,
    -.09804095327854,
    .02291300706565,
    -.07438835501671,
    -.15710659325123,
    -.42748948931694,
    .04259072244167,
    -.35830733180046,
    -.09898918122053,
    .22108262777328,
    -.00701736938208,
    .0992873236537,
    .28958559036255,
    -.24864092469215,
    -.10584850609303,
    .21528913080692,
    .38809850811958,
    -.16492980718613,
    .16538816690445,
    .1459391862154,
    -.57048463821411,
    -.47923597693443,
    .19784018397331,
    -.4271782040596,
    -.65820020437241,
    .24511873722076,
    -.0877638310194,
    .02952514961362,
    .42909786105156,
    -.03132146969438,
    .57771807909012,
    .29522883892059,
    .6555985212326,
    .76207375526428,
    .05302781611681,
    .55105316638947,
    -.42574247717857,
    -.15540990233421,
    -.92604118585587,
    -.88112819194794,
    .75632172822952,
    -.25287514925003,
    .29006350040436,
    .45125409960747,
    -.41159069538116,
    -.44450175762177,
    .32716807723045,
    .48259317874908,
    .11487878113985,
    .70277869701385,
    .60241633653641,
    -.18233296275139,
    .85120695829391,
    -.37064728140831,
    3.2916390895844,
    -1.4963974952698,
    -.16283248364925,
    .56923681497574,
    -2.3088004589081,
    .51979947090149,
    1.1197309494019,
    .02996650896966,
    .40969428420067,
    1.9223841428757,
    -.21881568431854,
    2.9340152740479,
    -3.8318600654602,
    -6.239429473877,
    -.08245316892862,
    1.2339268922806,
    1.1695692539215])

yr = np.array([
    np.nan,
    .17000007629395,
    .17130389809608,
    -.03651398047805,
    .11960058659315,
    -.05888139456511,
    .14059536159039,
    .00932686589658,
    -.11167634278536,
    .04570783302188,
    -.0108770346269,
    -.00886330008507,
    .10282856971025,
    -.07230624556541,
    .08900293707848,
    -.0240114107728,
    -.03996651992202,
    .13645842671394,
    -.03366377204657,
    .10188252478838,
    -.09296332299709,
    -.01846401393414,
    .01477065030485,
    .0729955881834,
    .00248436117545,
    .10262141376734,
    -.04228436201811,
    .12465056031942,
    .27647939324379,
    .00560382334515,
    .23561419546604,
    -.1489082723856,
    .02448422275484,
    .12172746658325,
    .10437148809433,
    .18971465528011,
    .06232447177172,
    .25419962406158,
    .11730266362429,
    .10116269439459,
    .2875237762928,
    .14597341418266,
    .12589547038078,
    .20893961191177,
    .17959471046925,
    -.04518361017108,
    .06391899287701,
    .05659105628729,
    -.24960128962994,
    .09022761881351,
    -.12118522822857,
    -.10079623758793,
    -.08357158303261,
    -.06902338564396,
    .04326653853059,
    .13862533867359,
    .61968916654587,
    .02860714122653,
    .92676383256912,
    .59005337953568,
    .60461646318436,
    .41744044423103,
    .85882639884949,
    .33423033356667,
    -.31093180179596,
    .04102724045515,
    -.06013804674149,
    -.04610994458199,
    -.63425624370575,
    .06586220860481,
    .06031560897827,
    -.04437142238021,
    .46668976545334,
    -.09905604273081,
    -.07949769496918,
    .23702463507652,
    .30592212080956,
    .66463404893875,
    .56969320774078,
    .28950771689415,
    .95181107521057,
    1.1148544549942,
    .75411820411682,
    1.2484039068222,
    1.1690024137497,
    -.1975435167551,
    .24200716614723,
    .6153416633606,
    -.06725100427866,
    .45309436321259,
    -.10480991750956,
    -.97863000631332,
    -1.2213591337204,
    .8715478181839,
    -1.1508584022522,
    -1.7689031362534,
    -.39508575201035,
    -.22900961339474,
    -.18821682035923,
    .14623281359673,
    .03029025532305,
    -.36817568540573,
    -.10679569840431,
    -.48499462008476,
    .29349562525749,
    -.34534454345703,
    -.18803144991398,
    .44535079598427,
    -2.2165644168854,
    .12161350995302,
    .00687709869817,
    .50946187973022,
    .53653997182846,
    .25995117425919,
    .32527860999107,
    .08098815381527,
    .27360898256302,
    .33735024929047,
    .39170032739639,
    .23812144994736,
    .80789774656296,
    .19225835800171,
    -.33032417297363,
    .92568749189377,
    .09278241544962,
    .28566908836365,
    1.5496014356613,
    -.27607008814812,
    -1.1263961791992,
    -.24930645525455,
    -.30482992529869,
    -.15224970877171,
    -.12287864089012,
    -.09804095327854,
    .02291300706565,
    -.07438835501671,
    -.15710659325123,
    -.42748948931694,
    .04259072244167,
    -.35830733180046,
    -.09898918122053,
    .22108262777328,
    -.00701736938208,
    .0992873236537,
    .28958559036255,
    -.24864092469215,
    -.10584850609303,
    .21528913080692,
    .38809850811958,
    -.16492980718613,
    .16538816690445,
    .1459391862154,
    -.57048463821411,
    -.47923597693443,
    .19784018397331,
    -.4271782040596,
    -.65820020437241,
    .24511873722076,
    -.0877638310194,
    .02952514961362,
    .42909786105156,
    -.03132146969438,
    .57771807909012,
    .29522883892059,
    .6555985212326,
    .76207375526428,
    .05302781611681,
    .55105316638947,
    -.42574247717857,
    -.15540990233421,
    -.92604118585587,
    -.88112819194794,
    .75632172822952,
    -.25287514925003,
    .29006350040436,
    .45125409960747,
    -.41159069538116,
    -.44450175762177,
    .32716807723045,
    .48259317874908,
    .11487878113985,
    .70277869701385,
    .60241633653641,
    -.18233296275139,
    .85120695829391,
    -.37064728140831,
    3.2916390895844,
    -1.4963974952698,
    -.16283248364925,
    .56923681497574,
    -2.3088004589081,
    .51979947090149,
    1.1197309494019,
    .02996650896966,
    .40969428420067,
    1.9223841428757,
    -.21881568431854,
    2.9340152740479,
    -3.8318600654602,
    -6.239429473877,
    -.08245316892862,
    1.2339268922806,
    1.1695692539215])

mse = np.array([
    1.1112809181213,
    .6632194519043,
    .65879660844803,
    .65575885772705,
    .65364873409271,
    .65217137336731,
    .65113133192062,
    .6503963470459,
    .64987552165985,
    .64950579404831,
    .64924287796021,
    .64905577898026,
    .64892256259918,
    .64882761240005,
    .64875996112823,
    .64871168136597,
    .64867728948593,
    .64865279197693,
    .64863526821136,
    .64862281084061,
    .64861387014389,
    .64860755205154,
    .64860302209854,
    .64859980344772,
    .64859747886658,
    .64859586954117,
    .64859467744827,
    .64859384298325,
    .6485932469368,
    .64859282970428,
    .64859253168106,
    .64859229326248,
    .64859211444855,
    .64859199523926,
    .64859193563461,
    .64859187602997,
    .64859187602997,
    .64859181642532,
    .64859181642532,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068,
    .64859175682068])

stdp = np.array([
    0,
    0,
    .02869686298072,
    .05651443824172,
    .0503994859755,
    .06887971609831,
    .05940540507436,
    .08067328482866,
    .08167565613985,
    .06429278105497,
    .07087650150061,
    .06886467337608,
    .06716959923506,
    .08230647444725,
    .07099691033363,
    .08401278406382,
    .07996553182602,
    .07354256510735,
    .09366323798895,
    .08811800926924,
    .10296355187893,
    .08846370875835,
    .0852297320962,
    .08700425922871,
    .09751411527395,
    .09737934917212,
    .11228405684233,
    .1053489819169,
    .12352022528648,
    .16439816355705,
    .1643835157156,
    .19891132414341,
    .17551273107529,
    .17827558517456,
    .19562774896622,
    .21028305590153,
    .23767858743668,
    .24580039083958,
    .28269505500793,
    .29883882403374,
    .31247469782829,
    .35402658581734,
    .37410452961922,
    .39106267690659,
    .42040377855301,
    .44518512487411,
    .43608102202415,
    .44340893626213,
    .44959822297096,
    .40977239608765,
    .42118826508522,
    .40079545974731,
    .38357082009315,
    .36902260780334,
    .35673499107361,
    .36137464642525,
    .38031083345413,
    .47139286994934,
    .47323387861252,
    .60994738340378,
    .69538277387619,
    .7825602889061,
    .84117436408997,
    .9657689332962,
    1.0109325647354,
    .95897275209427,
    .96013957262039,
    .9461076259613,
    .9342554807663,
    .83413934707642,
    .83968591690063,
    .84437066316605,
    .83330947160721,
    .8990553021431,
    .87949693202972,
    .86297762393951,
    .89407861232758,
    .93536442518234,
    1.0303052663803,
    1.1104937791824,
    1.1481873989105,
    1.2851470708847,
    1.4458787441254,
    1.5515991449356,
    1.7309991121292,
    1.8975404500961,
    1.8579913377762,
    1.8846583366394,
    1.9672524929047,
    1.9469071626663,
    2.0048115253448,
    1.9786299467087,
    1.8213576078415,
    1.6284521818161,
    1.7508568763733,
    1.5689061880112,
    1.2950873374939,
    1.2290096282959,
    1.1882168054581,
    1.1537625789642,
    1.1697143316269,
    1.1681711673737,
    1.106795668602,
    1.0849931240082,
    1.006507396698,
    1.0453414916992,
    .98803448677063,
    .95465070009232,
    1.0165599584579,
    .67838954925537,
    .69311982393265,
    .69054269790649,
    .76345545053482,
    .84005492925644,
    .87471830844879,
    .91901183128357,
    .92638796567917,
    .96265280246735,
    1.0083012580872,
    1.0618740320206,
    1.0921038389206,
    1.2077431678772,
    1.2303256988525,
    1.174311041832,
    1.3072115182877,
    1.314337015152,
    1.3503924608231,
    1.5760731697083,
    1.5264053344727,
    1.34929728508,
    1.304829955101,
    1.2522557973862,
    1.222869515419,
    1.198047041893,
    1.1770839691162,
    1.1743944883347,
    1.1571066379547,
    1.1274864673615,
    1.0574153661728,
    1.058304309845,
    .99898308515549,
    .9789143204689,
    1.0070173740387,
    1.000718832016,
    1.0104174613953,
    1.0486439466476,
    1.0058424472809,
    .98470783233643,
    1.0119106769562,
    1.0649236440659,
    1.0346088409424,
    1.0540577173233,
    1.0704846382141,
    .97923594713211,
    .90216588973999,
    .9271782040596,
    .85819715261459,
    .75488126277924,
    .78776079416275,
    .77047789096832,
    .77089905738831,
    .8313245177269,
    .82229107618332,
    .90476810932159,
    .94439232349396,
    1.0379292964935,
    1.1469690799713,
    1.1489590406418,
    1.2257302999496,
    1.1554099321365,
    1.1260533332825,
    .9811190366745,
    .8436843752861,
    .95287209749222,
    .90993344783783,
    .94875508546829,
    1.0115815401077,
    .94450175762177,
    .87282890081406,
    .91741597652435,
    .98511207103729,
    .9972335100174,
    1.0975805521011,
    1.1823329925537,
    1.1487929821014,
    1.270641207695,
    1.2083609104156,
    1.696394443512,
    1.4628355503082,
    1.4307631254196,
    1.5087975263596,
    1.1542117595673,
    1.2262620925903,
    1.3880327939987,
    1.3853038549423,
    1.4396153688431,
    1.7208145856857,
    1.678991317749,
    2.110867023468,
    1.524417757988,
    .57946246862411,
    .56406193971634,
    .74643105268478])

icstats = np.array([
    202,
    np.nan,
    -242.89663276735,
    3,
    491.79326553469,
    501.7180686269])


results = Bunch(
    llf=llf,
    nobs=nobs,
    k=k,
    k_exog=k_exog,
    sigma=sigma,
    chi2=chi2,
    df_model=df_model,
    k_ar=k_ar,
    k_ma=k_ma,
    params=params,
    cov_params=cov_params,
    xb=xb,
    y=y,
    resid=resid,
    yr=yr,
    mse=mse,
    stdp=stdp,
    icstats=icstats
)
