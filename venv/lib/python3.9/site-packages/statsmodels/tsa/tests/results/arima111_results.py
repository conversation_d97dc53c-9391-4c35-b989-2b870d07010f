import numpy as np

from statsmodels.tools.tools import Bunch

llf = np.array([-241.75576160303])

nobs = np.array([202])

k = np.array([4])

k_exog = np.array([1])

sigma = np.array([.79987660416529])

chi2 = np.array([342.91413339514])

df_model = np.array([2])

k_ar = np.array([1])

k_ma = np.array([1])

params = np.array([
    .88084748605315,
    .93989719451385,
    -.7709851377434,
    .79987660416529])

cov_params = np.array([
    .15020189867396,
    -.01642122563089,
    .01456018801049,
    -.00156750041014,
    -.01642122563089,
    .0032067778715,
    -.00350387326241,
    .00059634328354,
    .01456018801049,
    -.00350387326241,
    .00480028434835,
    -.00068065418463,
    -.00156750041014,
    .00059634328354,
    -.00068065418463,
    .00029322997097]).reshape(4, 4)

xb = np.array([
    .88084751367569,
    .88084751367569,
    .65303039550781,
    .55365419387817,
    .45908725261688,
    .42810925841331,
    .37837743759155,
    .37686342000961,
    .35719576478004,
    .3220648765564,
    .31943875551224,
    .30907514691353,
    .30120712518692,
    .31383177638054,
    .29652059078217,
    .30856171250343,
    .30095273256302,
    .29171526432037,
    .31331890821457,
    .30463594198227,
    .31990340352058,
    .30126947164536,
    .29703867435455,
    .29884466528893,
    .31037190556526,
    .30912432074547,
    .32505416870117,
    .31537705659866,
    .33494210243225,
    .37874156236649,
    .37366089224815,
    .40859284996986,
    .37640652060509,
    .37692713737488,
    .39422073960304,
    .40755322575569,
    .43472331762314,
    .43878075480461,
    .47569087147713,
    .48725643754005,
    .49617394804955,
    .53683114051819,
    .55128628015518,
    .56243091821671,
    .58791494369507,
    .60756206512451,
    .58892780542374,
    .59145200252533,
    .59339815378189,
    .54422444105148,
    .55698639154434,
    .53304374217987,
    .51458370685577,
    .50035130977631,
    .48937830328941,
    .49780988693237,
    .52120143175125,
    .62369203567505,
    .6182547211647,
    .76608312129974,
    .84627467393875,
    .92499214410782,
    .96879118680954,
    1.0870156288147,
    1.1105998754501,
    1.0274360179901,
    1.013991355896,
    .98673474788666,
    .96571969985962,
    .84817039966583,
    .85888928174973,
    .86715340614319,
    .85663330554962,
    .93297851085663,
    .90738350152969,
    .88765007257462,
    .92311006784439,
    .96734017133713,
    1.0690053701401,
    1.1473876237869,
    1.1740373373032,
    1.3128218650818,
    1.4704967737198,
    1.5582785606384,
    1.7273052930832,
    1.8745132684708,
    1.7853132486343,
    1.7841064929962,
    1.850741147995,
    1.800768494606,
    1.8466963768005,
    1.7976499795914,
    1.6078149080276,
    1.3938897848129,
    1.5498898029327,
    1.3492304086685,
    1.059396147728,
    1.0217411518097,
    1.0096007585526,
    1.0002405643463,
    1.0436969995499,
    1.0603114366531,
    1.0055546760559,
    .99712115526199,
    .92305397987366,
    .9841884970665,
    .92997401952744,
    .90506774187088,
    .9872123003006,
    .61137217283249,
    .65943044424057,
    .67959040403366,
    .77959072589874,
    .87357920408249,
    .91226226091385,
    .95897603034973,
    .96120971441269,
    .99671375751495,
    1.0409790277481,
    1.0919979810715,
    1.1144404411316,
    1.2330915927887,
    1.2401138544083,
    1.161071896553,
    1.3028255701065,
    1.2938764095306,
    1.3207612037659,
    1.5610725879669,
    1.4760913848877,
    1.258552312851,
    1.2090681791306,
    1.1540271043777,
    1.12848341465,
    1.1087870597839,
    1.0936040878296,
    1.0987877845764,
    1.0858948230743,
    1.0590622425079,
    .98770052194595,
    1.0002481937408,
    .94235575199127,
    .93150353431702,
    .97381073236465,
    .9726470708847,
    .98864215612411,
    1.0347559452057,
    .98585307598114,
    .96503925323486,
    .9996662735939,
    1.0601476430893,
    1.022319316864,
    1.043828368187,
    1.0604115724564,
    .95495897531509,
    .87365657091141,
    .91232192516327,
    .84078407287598,
    .73495537042618,
    .78849309682846,
    .77909576892853,
    .78874284029007,
    .8637443780899,
    .8540056347847,
    .94784545898438,
    .98641014099121,
    1.0837067365646,
    1.1925053596497,
    1.1750392913818,
    1.2460317611694,
    1.1487410068512,
    1.1075156927109,
    .94060403108597,
    .7950227856636,
    .93615245819092,
    .89293897151947,
    .94407802820206,
    1.0172899961472,
    .93860250711441,
    .86104601621628,
    .91948908567429,
    .99833220243454,
    1.008442401886,
    1.1175880432129,
    1.2017351388931,
    1.1483734846115,
    1.2761443853378,
    1.188849568367,
    1.7296310663223,
    1.4202431440353,
    1.3675138950348,
    1.445098400116,
    1.031960606575,
    1.1313284635544,
    1.3214453458786,
    1.3112732172012,
    1.367110490799,
    1.674845457077,
    1.5979281663895,
    2.064112663269,
    1.3536450862885,
    .30015936493874,
    .36831066012383,
    .64060544967651])

y = np.array([
    np.nan,
    29.860847473145,
    29.803030014038,
    29.903654098511,
    29.82908821106,
    29.968111038208,
    29.928377151489,
    30.126863479614,
    30.197195053101,
    30.132064819336,
    30.23943901062,
    30.289073944092,
    30.341207504272,
    30.523830413818,
    30.516519546509,
    30.68856048584,
    30.740953445435,
    30.771715164185,
    31.003318786621,
    31.054636001587,
    31.25990486145,
    31.251270294189,
    31.317039489746,
    31.418846130371,
    31.590372085571,
    31.689123153687,
    31.905054092407,
    31.965375900269,
    32.214942932129,
    32.658740997314,
    32.823661804199,
    33.258590698242,
    33.27640914917,
    33.47692489624,
    33.7942237854,
    34.107555389404,
    34.534721374512,
    34.83878326416,
    35.375694274902,
    35.787254333496,
    36.196174621582,
    36.83683013916,
    37.3512840271,
    37.86243057251,
    38.487915039063,
    39.107563018799,
    39.488929748535,
    39.991455078125,
    40.49340057373,
    40.644222259521,
    41.156986236572,
    41.433044433594,
    41.714584350586,
    42.000350952148,
    42.289379119873,
    42.697811126709,
    43.221202850342,
    44.323692321777,
    44.818256378174,
    46.366081237793,
    47.64627456665,
    49.024990081787,
    50.26879119873,
    52.087017059326,
    53.410598754883,
    54.027435302734,
    55.01399230957,
    55.886737823486,
    56.765720367432,
    56.948169708252,
    57.858890533447,
    58.767154693604,
    59.556632995605,
    60.93297958374,
    61.707382202148,
    62.487648010254,
    63.623111724854,
    64.867340087891,
    66.569007873535,
    68.247383117676,
    69.674034118652,
    71.912818908691,
    74.470497131348,
    76.758277893066,
    79.72730255127,
    82.774513244629,
    84.385314941406,
    86.484100341797,
    89.050735473633,
    90.900764465332,
    93.346694946289,
    95.197654724121,
    96.007820129395,
    96.393890380859,
    99.04988861084,
    99.449226379395,
    98.959396362305,
    99.821746826172,
    100.80960083008,
    101.80024719238,
    103.1436920166,
    104.36031341553,
    105.10555267334,
    106.09712219238,
    106.62305450439,
    107.98419189453,
    108.62997436523,
    109.40506744385,
    110.88721466064,
    109.31137084961,
    110.15943145752,
    110.87958526611,
    112.17959594727,
    113.57357788086,
    114.71226501465,
    115.95897674561,
    116.9612121582,
    118.1967086792,
    119.54097747803,
    120.99199676514,
    122.31443786621,
    124.33309173584,
    125.74011230469,
    126.56107330322,
    128.80282592773,
    130.19386291504,
    131.82075500488,
    134.96105957031,
    136.17608642578,
    136.35855102539,
    137.40905761719,
    138.35401916504,
    139.42848205566,
    140.50877380371,
    141.59359741211,
    142.79878234863,
    143.88589477539,
    144.85906982422,
    145.48770141602,
    146.60025024414,
    147.24235534668,
    148.13150024414,
    149.37380981445,
    150.3726348877,
    151.48864746094,
    152.83476257324,
    153.58586120605,
    154.46504211426,
    155.69966125488,
    157.16015625,
    158.0223236084,
    159.24382019043,
    160.46040344238,
    160.85494995117,
    161.27365112305,
    162.41232299805,
    162.84078979492,
    162.93495178223,
    163.98849487305,
    164.67909240723,
    165.48873901367,
    166.76373291016,
    167.55400085449,
    169.0478515625,
    170.2864074707,
    171.98370361328,
    173.89250183105,
    175.07502746582,
    176.84603881836,
    177.54873657227,
    178.50750732422,
    178.5406036377,
    178.49502563477,
    180.23616027832,
    180.89294433594,
    182.14407348633,
    183.61729431152,
    184.13859558105,
    184.56105041504,
    185.81948852539,
    187.29833984375,
    188.40843200684,
    190.21759033203,
    192.00173950195,
    192.9483795166,
    195.07614135742,
    195.88883972168,
    200.92962646484,
    200.82023620605,
    202.06750488281,
    204.1450958252,
    202.93196105957,
    204.70533752441,
    207.24143981934,
    208.6492767334,
    210.50010681152,
    214.16984558105,
    215.59492492676,
    220.67411804199,
    218.24264526367,
    212.47415161133,
    213.03932189941,
    215.10960388184])

resid = np.array([
    np.nan,
    -.71084743738174,
    -.45302960276604,
    -.5336537361145,
    -.28908717632294,
    -.41811093688011,
    -.17837668955326,
    -.28686326742172,
    -.38719645142555,
    -.21206425130367,
    -.25943928956985,
    -.24907378852367,
    -.13120894134045,
    -.3038315474987,
    -.13652075827122,
    -.24856032431126,
    -.26095372438431,
    -.0817142650485,
    -.25331944227219,
    -.11463540792465,
    -.30990317463875,
    -.2312697917223,
    -.19703827798367,
    -.13884480297565,
    -.21037344634533,
    -.10912357270718,
    -.25505447387695,
    -.08537751436234,
    .06505750864744,
    -.20873957872391,
    .02633681893349,
    -.35858979821205,
    -.1764095723629,
    -.07692407816648,
    -.09422151744366,
    -.00755552388728,
    -.13472028076649,
    .06121923774481,
    -.07569316774607,
    -.08725491166115,
    .10382451862097,
    -.03683112934232,
    -.05128625407815,
    .03757134452462,
    .0120835499838,
    -.20756052434444,
    -.08892779797316,
    -.09145200997591,
    -.3934012055397,
    -.04422445222735,
    -.25698333978653,
    -.23304453492165,
    -.21458448469639,
    -.2003520578146,
    -.08937677741051,
    .00219011562876,
    .47879853844643,
    -.12369203567505,
    .78174299001694,
    .43391767144203,
    .4537245631218,
    .27500861883163,
    .73120957612991,
    .21298357844353,
    -.41059911251068,
    -.02743596211076,
    -.11398979276419,
    -.08673703670502,
    -.66572046279907,
    .05183110013604,
    .04111221805215,
    -.06715416908264,
    .44336593151093,
    -.13297925889492,
    -.1073842421174,
    .21235218644142,
    .27689066529274,
    .63265830278397,
    .53099316358566,
    .25261387228966,
    .92596107721329,
    1.0871796607971,
    .72950023412704,
    1.2417244911194,
    1.1726962327957,
    -.17451636493206,
    .31468516588211,
    .71589350700378,
    .04926039651036,
    .59923303127289,
    .05330519750714,
    -.79764997959137,
    -1.0078164339066,
    1.1061102151871,
    -.94989138841629,
    -1.5492273569107,
    -.15939457714558,
    -.02174116671085,
    -.00960071571171,
    .29975482821465,
    .15630762279034,
    -.2603160738945,
    -.00555467186496,
    -.3971226811409,
    .37694907188416,
    -.28419154882431,
    -.12997098267078,
    .49493381381035,
    -2.1872169971466,
    .18863087892532,
    .04056651890278,
    .52041417360306,
    .52040469646454,
    .22642692923546,
    .28773468732834,
    .0410239957273,
    .2387872338295,
    .30328929424286,
    .35902243852615,
    .20799747109413,
    .78556102514267,
    .16690990328789,
    -.34011232852936,
    .93892657756805,
    .0971682742238,
    .30612966418266,
    1.5792326927185,
    -.26106956601143,
    -1.0760822296143,
    -.15856145322323,
    -.2090682387352,
    -.05402099713683,
    -.02849259786308,
    -.00878097955137,
    .10639289021492,
    .00121826829854,
    -.08589478582144,
    -.35906526446342,
    .11230555176735,
    -.30025118589401,
    -.04236188530922,
    .26849341392517,
    .02618926763535,
    .12735903263092,
    .31136092543602,
    -.23475293815136,
    -.08585914969444,
    .23495768010616,
    .40034285187721,
    -.1601537913084,
    .17767761647701,
    .15616858005524,
    -.56041151285172,
    -.45495894551277,
    .2263495028019,
    -.41232195496559,
    -.64078712463379,
    .26504465937614,
    -.08849616348743,
    .02090725488961,
    .41125410795212,
    -.06374131888151,
    .54600352048874,
    .25215145945549,
    .61358070373535,
    .71629631519318,
    .00749156065285,
    .52497291564941,
    -.44604399800301,
    -.14874097704887,
    -.90750348567963,
    -.84061318635941,
    .80498331785202,
    -.23615552484989,
    .30705797672272,
    .45593112707138,
    -.41729912161827,
    -.43860253691673,
    .33895090222359,
    .48052009940147,
    .10165861994028,
    .69156980514526,
    .58240884542465,
    -.20173519849777,
    .85162657499313,
    -.37615045905113,
    3.3111503124237,
    -1.5296341180801,
    -.12024004757404,
    .63248610496521,
    -2.2451014518738,
    .64205056428909,
    1.2146645784378,
    .09655395895243,
    .48372489213943,
    1.9948890209198,
    -.17284658551216,
    3.0150785446167,
    -3.7851057052612,
    -6.0686569213867,
    .19684991240501,
    1.4296782016754,
    1.2753949165344])

yr = np.array([
    np.nan,
    -.71084743738174,
    -.45302960276604,
    -.5336537361145,
    -.28908717632294,
    -.41811093688011,
    -.17837668955326,
    -.28686326742172,
    -.38719645142555,
    -.21206425130367,
    -.25943928956985,
    -.24907378852367,
    -.13120894134045,
    -.3038315474987,
    -.13652075827122,
    -.24856032431126,
    -.26095372438431,
    -.0817142650485,
    -.25331944227219,
    -.11463540792465,
    -.30990317463875,
    -.2312697917223,
    -.19703827798367,
    -.13884480297565,
    -.21037344634533,
    -.10912357270718,
    -.25505447387695,
    -.08537751436234,
    .06505750864744,
    -.20873957872391,
    .02633681893349,
    -.35858979821205,
    -.1764095723629,
    -.07692407816648,
    -.09422151744366,
    -.00755552388728,
    -.13472028076649,
    .06121923774481,
    -.07569316774607,
    -.08725491166115,
    .10382451862097,
    -.03683112934232,
    -.05128625407815,
    .03757134452462,
    .0120835499838,
    -.20756052434444,
    -.08892779797316,
    -.09145200997591,
    -.3934012055397,
    -.04422445222735,
    -.25698333978653,
    -.23304453492165,
    -.21458448469639,
    -.2003520578146,
    -.08937677741051,
    .00219011562876,
    .47879853844643,
    -.12369203567505,
    .78174299001694,
    .43391767144203,
    .4537245631218,
    .27500861883163,
    .73120957612991,
    .21298357844353,
    -.41059911251068,
    -.02743596211076,
    -.11398979276419,
    -.08673703670502,
    -.66572046279907,
    .05183110013604,
    .04111221805215,
    -.06715416908264,
    .44336593151093,
    -.13297925889492,
    -.1073842421174,
    .21235218644142,
    .27689066529274,
    .63265830278397,
    .53099316358566,
    .25261387228966,
    .92596107721329,
    1.0871796607971,
    .72950023412704,
    1.2417244911194,
    1.1726962327957,
    -.17451636493206,
    .31468516588211,
    .71589350700378,
    .04926039651036,
    .59923303127289,
    .05330519750714,
    -.79764997959137,
    -1.0078164339066,
    1.1061102151871,
    -.94989138841629,
    -1.5492273569107,
    -.15939457714558,
    -.02174116671085,
    -.00960071571171,
    .29975482821465,
    .15630762279034,
    -.2603160738945,
    -.00555467186496,
    -.3971226811409,
    .37694907188416,
    -.28419154882431,
    -.12997098267078,
    .49493381381035,
    -2.1872169971466,
    .18863087892532,
    .04056651890278,
    .52041417360306,
    .52040469646454,
    .22642692923546,
    .28773468732834,
    .0410239957273,
    .2387872338295,
    .30328929424286,
    .35902243852615,
    .20799747109413,
    .78556102514267,
    .16690990328789,
    -.34011232852936,
    .93892657756805,
    .0971682742238,
    .30612966418266,
    1.5792326927185,
    -.26106956601143,
    -1.0760822296143,
    -.15856145322323,
    -.2090682387352,
    -.05402099713683,
    -.02849259786308,
    -.00878097955137,
    .10639289021492,
    .00121826829854,
    -.08589478582144,
    -.35906526446342,
    .11230555176735,
    -.30025118589401,
    -.04236188530922,
    .26849341392517,
    .02618926763535,
    .12735903263092,
    .31136092543602,
    -.23475293815136,
    -.08585914969444,
    .23495768010616,
    .40034285187721,
    -.1601537913084,
    .17767761647701,
    .15616858005524,
    -.56041151285172,
    -.45495894551277,
    .2263495028019,
    -.41232195496559,
    -.64078712463379,
    .26504465937614,
    -.08849616348743,
    .02090725488961,
    .41125410795212,
    -.06374131888151,
    .54600352048874,
    .25215145945549,
    .61358070373535,
    .71629631519318,
    .00749156065285,
    .52497291564941,
    -.44604399800301,
    -.14874097704887,
    -.90750348567963,
    -.84061318635941,
    .80498331785202,
    -.23615552484989,
    .30705797672272,
    .45593112707138,
    -.41729912161827,
    -.43860253691673,
    .33895090222359,
    .48052009940147,
    .10165861994028,
    .69156980514526,
    .58240884542465,
    -.20173519849777,
    .85162657499313,
    -.37615045905113,
    3.3111503124237,
    -1.5296341180801,
    -.12024004757404,
    .63248610496521,
    -2.2451014518738,
    .64205056428909,
    1.2146645784378,
    .09655395895243,
    .48372489213943,
    1.9948890209198,
    -.17284658551216,
    3.0150785446167,
    -3.7851057052612,
    -6.0686569213867,
    .19684991240501,
    1.4296782016754,
    1.2753949165344])

mse = np.array([
    .7963672876358,
    .7963672876358,
    .71457105875015,
    .67959600687027,
    .66207146644592,
    .65259438753128,
    .64725720882416,
    .644182741642,
    .64238852262497,
    .64133352041245,
    .64071041345596,
    .6403414607048,
    .64012265205383,
    .63999271392822,
    .63991558551788,
    .63986974954605,
    .63984251022339,
    .63982629776001,
    .6398167014122,
    .6398109793663,
    .63980758190155,
    .63980555534363,
    .63980436325073,
    .639803647995,
    .63980323076248,
    .63980293273926,
    .63980281352997,
    .63980269432068,
    .63980263471603,
    .63980263471603,
    .63980263471603,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139,
    .63980257511139])

stdp = np.array([
    .88084751367569,
    .88084751367569,
    .65303039550781,
    .55365419387817,
    .45908725261688,
    .42810925841331,
    .37837743759155,
    .37686342000961,
    .35719576478004,
    .3220648765564,
    .31943875551224,
    .30907514691353,
    .30120712518692,
    .31383177638054,
    .29652059078217,
    .30856171250343,
    .30095273256302,
    .29171526432037,
    .31331890821457,
    .30463594198227,
    .31990340352058,
    .30126947164536,
    .29703867435455,
    .29884466528893,
    .31037190556526,
    .30912432074547,
    .32505416870117,
    .31537705659866,
    .33494210243225,
    .37874156236649,
    .37366089224815,
    .40859284996986,
    .37640652060509,
    .37692713737488,
    .39422073960304,
    .40755322575569,
    .43472331762314,
    .43878075480461,
    .47569087147713,
    .48725643754005,
    .49617394804955,
    .53683114051819,
    .55128628015518,
    .56243091821671,
    .58791494369507,
    .60756206512451,
    .58892780542374,
    .59145200252533,
    .59339815378189,
    .54422444105148,
    .55698639154434,
    .53304374217987,
    .51458370685577,
    .50035130977631,
    .48937830328941,
    .49780988693237,
    .52120143175125,
    .62369203567505,
    .6182547211647,
    .76608312129974,
    .84627467393875,
    .92499214410782,
    .96879118680954,
    1.0870156288147,
    1.1105998754501,
    1.0274360179901,
    1.013991355896,
    .98673474788666,
    .96571969985962,
    .84817039966583,
    .85888928174973,
    .86715340614319,
    .85663330554962,
    .93297851085663,
    .90738350152969,
    .88765007257462,
    .92311006784439,
    .96734017133713,
    1.0690053701401,
    1.1473876237869,
    1.1740373373032,
    1.3128218650818,
    1.4704967737198,
    1.5582785606384,
    1.7273052930832,
    1.8745132684708,
    1.7853132486343,
    1.7841064929962,
    1.850741147995,
    1.800768494606,
    1.8466963768005,
    1.7976499795914,
    1.6078149080276,
    1.3938897848129,
    1.5498898029327,
    1.3492304086685,
    1.059396147728,
    1.0217411518097,
    1.0096007585526,
    1.0002405643463,
    1.0436969995499,
    1.0603114366531,
    1.0055546760559,
    .99712115526199,
    .92305397987366,
    .9841884970665,
    .92997401952744,
    .90506774187088,
    .9872123003006,
    .61137217283249,
    .65943044424057,
    .67959040403366,
    .77959072589874,
    .87357920408249,
    .91226226091385,
    .95897603034973,
    .96120971441269,
    .99671375751495,
    1.0409790277481,
    1.0919979810715,
    1.1144404411316,
    1.2330915927887,
    1.2401138544083,
    1.161071896553,
    1.3028255701065,
    1.2938764095306,
    1.3207612037659,
    1.5610725879669,
    1.4760913848877,
    1.258552312851,
    1.2090681791306,
    1.1540271043777,
    1.12848341465,
    1.1087870597839,
    1.0936040878296,
    1.0987877845764,
    1.0858948230743,
    1.0590622425079,
    .98770052194595,
    1.0002481937408,
    .94235575199127,
    .93150353431702,
    .97381073236465,
    .9726470708847,
    .98864215612411,
    1.0347559452057,
    .98585307598114,
    .96503925323486,
    .9996662735939,
    1.0601476430893,
    1.022319316864,
    1.043828368187,
    1.0604115724564,
    .95495897531509,
    .87365657091141,
    .91232192516327,
    .84078407287598,
    .73495537042618,
    .78849309682846,
    .77909576892853,
    .78874284029007,
    .8637443780899,
    .8540056347847,
    .94784545898438,
    .98641014099121,
    1.0837067365646,
    1.1925053596497,
    1.1750392913818,
    1.2460317611694,
    1.1487410068512,
    1.1075156927109,
    .94060403108597,
    .7950227856636,
    .93615245819092,
    .89293897151947,
    .94407802820206,
    1.0172899961472,
    .93860250711441,
    .86104601621628,
    .91948908567429,
    .99833220243454,
    1.008442401886,
    1.1175880432129,
    1.2017351388931,
    1.1483734846115,
    1.2761443853378,
    1.188849568367,
    1.7296310663223,
    1.4202431440353,
    1.3675138950348,
    1.445098400116,
    1.031960606575,
    1.1313284635544,
    1.3214453458786,
    1.3112732172012,
    1.367110490799,
    1.674845457077,
    1.5979281663895,
    2.064112663269,
    1.3536450862885,
    .30015936493874,
    .36831066012383,
    .64060544967651])

icstats = np.array([
    202,
    np.nan,
    -241.75576160303,
    4,
    491.51152320605,
    504.74459399566])


results = Bunch(
    llf=llf,
    nobs=nobs,
    k=k,
    k_exog=k_exog,
    sigma=sigma,
    chi2=chi2,
    df_model=df_model,
    k_ar=k_ar,
    k_ma=k_ma,
    params=params,
    cov_params=cov_params,
    xb=xb,
    y=y,
    resid=resid,
    yr=yr,
    mse=mse,
    stdp=stdp,
    icstats=icstats
)
