import numpy as np

from statsmodels.tools.tools import Bunch

llf = np.array([-241.25977940638])

nobs = np.array([202])

k = np.array([4])

k_exog = np.array([1])

sigma = np.array([.79533686587485])

chi2 = np.array([48655.961417345])

df_model = np.array([3])

k_ar = np.array([2])

k_ma = np.array([1])

params = np.array([
    1.1870704073154,
    -.19095698898571,
    -.90853757573555,
    .79533686587485])

cov_params = np.array([
    .00204336743511,
    -.00177522179187,
    -.00165894353702,
    -.00031352141782,
    -.00177522179187,
    .00157376214003,
    .00132907629148,
    .00030367391511,
    -.00165894353702,
    .00132907629148,
    .00210988984438,
    .00024199988464,
    -.00031352141782,
    .00030367391511,
    .00024199988464,
    .00027937875185]).reshape(4, 4)

xb = np.array([
    0,
    0,
    .11248598247766,
    .14283391833305,
    .0800810828805,
    .12544548511505,
    .07541109621525,
    .1297073662281,
    .10287435352802,
    .06303016841412,
    .09501431882381,
    .08120259642601,
    .07862555980682,
    .10874316096306,
    .06787430495024,
    .10527064651251,
    .08142036944628,
    .07337106764317,
    .11828763782978,
    .08380854874849,
    .11801292747259,
    .07338324189186,
    .0842502862215,
    .09106454998255,
    .10832596570253,
    .09570593386889,
    .1236881390214,
    .09362822026014,
    .13587079942226,
    .19111332297325,
    .14459040760994,
    .21043147146702,
    .12866979837418,
    .16308072209358,
    .19356986880302,
    .20215991139412,
    .23782986402512,
    .22326464951038,
    .28485587239265,
    .27474755048752,
    .28465977311134,
    .34938132762909,
    .3421268761158,
    .35463020205498,
    .39384591579437,
    .41037485003471,
    .36968034505844,
    .39875456690788,
    .40607318282127,
    .32915702462196,
    .40012913942337,
    .35161358118057,
    .34572568535805,
    .34037715196609,
    .3355179131031,
    .35895752906799,
    .38901025056839,
    .53648668527603,
    .43572762608528,
    .69034379720688,
    .69410443305969,
    .76356476545334,
    .77972346544266,
    .95276647806168,
    .9030898809433,
    .76722019910812,
    .84191131591797,
    .82463103532791,
    .82802563905716,
    .66399103403091,
    .79665386676788,
    .80260843038559,
    .78016436100006,
    .91813576221466,
    .80874294042587,
    .80483394861221,
    .8848432302475,
    .92809981107712,
    1.0597171783447,
    1.1029140949249,
    1.0864543914795,
    1.3046631813049,
    1.4528053998947,
    1.4744025468826,
    1.6993381977081,
    1.816978096962,
    1.5705223083496,
    1.6871707439423,
    1.8281806707382,
    1.7127912044525,
    1.8617957830429,
    1.7624272108078,
    1.5169456005096,
    1.3543643951416,
    1.8122490644455,
    1.3362231254578,
    1.0437293052673,
    1.2371381521225,
    1.2306576967239,
    1.2056746482849,
    1.2665351629257,
    1.2366921901703,
    1.1172571182251,
    1.1408381462097,
    1.0126565694809,
    1.1675561666489,
    1.0074961185455,
    1.0045058727264,
    1.1498116254807,
    .44306626915932,
    .85451871156693,
    .81856834888458,
    .94427144527435,
    .99084824323654,
    .95836746692657,
    .994897544384,
    .95328682661057,
    1.0093784332275,
    1.0500040054321,
    1.0956697463989,
    1.090208530426,
    1.2714649438858,
    1.1823015213013,
    1.0575052499771,
    1.373840212822,
    1.2371203899384,
    1.3022859096527,
    1.6853868961334,
    1.3395566940308,
    1.0802086591721,
    1.2114092111588,
    1.1690926551819,
    1.1775953769684,
    1.1662193536758,
    1.1558910608292,
    1.1743551492691,
    1.1441857814789,
    1.1080147027969,
    1.0106881856918,
    1.0909667015076,
    .97610247135162,
    1.0038343667984,
    1.0743995904922,
    1.0255174636841,
    1.0471519231796,
    1.1034165620804,
    .97707790136337,
    .9856236577034,
    1.0578545331955,
    1.1219012737274,
    1.0026258230209,
    1.0733016729355,
    1.0802255868912,
    .89154416322708,
    .85378932952881,
    .98660898208618,
    .82558387517929,
    .71030122041702,
    .88567733764648,
    .80868631601334,
    .82387971878052,
    .92999804019928,
    .83861750364304,
    .99909782409668,
    .97461491823196,
    1.1019765138626,
    1.1970175504684,
    1.0780508518219,
    1.2238110303879,
    1.0100719928741,
    1.0434579849243,
    .81277370452881,
    .72809249162674,
    1.0880596637726,
    .87798285484314,
    .99824965000153,
    1.0677480697632,
    .86986482143402,
    .81499886512756,
    .97921711206436,
    1.0504562854767,
    .99342101812363,
    1.1660186052322,
    1.208247423172,
    1.0516448020935,
    1.3215674161911,
    1.0694575309753,
    2.0531799793243,
    1.0617904663086,
    1.2885792255402,
    1.4795436859131,
    .73947989940643,
    1.290878534317,
    1.506583571434,
    1.3157633543015,
    1.424609541893,
    1.8879710435867,
    1.4916514158249,
    2.3532779216766,
    .77780252695084,
    -.27798706293106,
    .7862361073494,
    1.1202166080475])

y = np.array([
    np.nan,
    28.979999542236,
    29.26248550415,
    29.492834091187,
    29.450082778931,
    29.665447235107,
    29.625410079956,
    29.879707336426,
    29.942874908447,
    29.873029708862,
    30.015014648438,
    30.06120300293,
    30.118625640869,
    30.318742752075,
    30.287874221802,
    30.485269546509,
    30.521421432495,
    30.553371429443,
    30.808288574219,
    30.833808898926,
    31.058013916016,
    31.023384094238,
    31.104249954224,
    31.211065292358,
    31.388326644897,
    31.475704193115,
    31.703687667847,
    31.743627548218,
    32.015869140625,
    32.471111297607,
    32.594593048096,
    33.060428619385,
    33.028671264648,
    33.263080596924,
    33.593570709229,
    33.902160644531,
    34.337829589844,
    34.623264312744,
    35.184856414795,
    35.574745178223,
    35.984661102295,
    36.649379730225,
    37.142127990723,
    37.654628753662,
    38.293846130371,
    38.910373687744,
    39.269680023193,
    39.798755645752,
    40.306076049805,
    40.42915725708,
    41.00012588501,
    41.251613616943,
    41.545726776123,
    41.840377807617,
    42.135517120361,
    42.558959960938,
    43.089012145996,
    44.236488342285,
    44.635726928711,
    46.290340423584,
    47.494102478027,
    48.863563537598,
    50.079723358154,
    51.952766418457,
    53.203090667725,
    53.767219543457,
    54.841911315918,
    55.724632263184,
    56.628025054932,
    56.763988494873,
    57.796653747559,
    58.702610015869,
    59.480163574219,
    60.91813659668,
    61.608741760254,
    62.404830932617,
    63.584842681885,
    64.828102111816,
    66.559715270996,
    68.202911376953,
    69.586456298828,
    71.904663085938,
    74.45280456543,
    76.67440032959,
    79.699340820313,
    82.716979980469,
    84.170516967773,
    86.387168884277,
    89.028175354004,
    90.812789916992,
    93.361793518066,
    95.16242980957,
    95.916946411133,
    96.354362487793,
    99.31224822998,
    99.436218261719,
    98.943733215332,
    100.03713989258,
    101.03066253662,
    102.00567626953,
    103.36653137207,
    104.5366973877,
    105.21725463867,
    106.24083709717,
    106.71265411377,
    108.1675567627,
    108.70749664307,
    109.50450897217,
    111.04981231689,
    109.14306640625,
    110.35451507568,
    111.01856231689,
    112.34427642822,
    113.6908416748,
    114.7583694458,
    115.99489593506,
    116.95328521729,
    118.20937347412,
    119.55000305176,
    120.9956741333,
    122.29020690918,
    124.37145996094,
    125.68230438232,
    126.45750427246,
    128.87384033203,
    130.13711547852,
    131.80229187012,
    135.08538818359,
    136.03955078125,
    136.18022155762,
    137.4114074707,
    138.36909484863,
    139.47760009766,
    140.56620788574,
    141.65588378906,
    142.87435913086,
    143.94418334961,
    144.90802001953,
    145.51068115234,
    146.69097900391,
    147.27610778809,
    148.2038269043,
    149.47439575195,
    150.4255065918,
    151.5471496582,
    152.90342712402,
    153.57708740234,
    154.4856262207,
    155.75785827637,
    157.22190856934,
    158.00262451172,
    159.2733001709,
    160.48022460938,
    160.79153442383,
    161.25378417969,
    162.4866027832,
    162.82557678223,
    162.9102935791,
    164.08567810059,
    164.70867919922,
    165.52388000488,
    166.82998657227,
    167.53861999512,
    169.09910583496,
    170.27461242676,
    172.00196838379,
    173.89701843262,
    174.97804260254,
    176.82382202148,
    177.41006469727,
    178.44345092773,
    178.41278076172,
    178.42808532715,
    180.38806152344,
    180.87797546387,
    182.1982421875,
    183.66775512695,
    184.06985473633,
    184.51499938965,
    185.87921142578,
    187.35046386719,
    188.3934173584,
    190.26602172852,
    192.00825500488,
    192.85165405273,
    195.12156677246,
    195.76945495605,
    201.25317382813,
    200.4617767334,
    201.98857116699,
    204.17953491211,
    202.63948059082,
    204.86488342285,
    207.42657470703,
    208.65376281738,
    210.55760192871,
    214.38296508789,
    215.48864746094,
    220.96327209473,
    217.66680908203,
    211.89601135254,
    213.45724487305,
    215.58921813965])

resid = np.array([
    np.nan,
    .17000007629395,
    .08751478046179,
    -.12283346056938,
    .08991899341345,
    -.11544716358185,
    .12458966672421,
    -.03970721364021,
    -.13287504017353,
    .04697044193745,
    -.03501485288143,
    -.02120122499764,
    .09137260913849,
    -.09874293208122,
    .09212554246187,
    -.04526927694678,
    -.04142136126757,
    .13662992417812,
    -.0582881718874,
    .10619198530912,
    -.10801269859076,
    -.00338354869746,
    .01575009897351,
    .06893529742956,
    -.00832748971879,
    .10429482907057,
    -.05368844047189,
    .13637132942677,
    .26412883400917,
    -.02111134678125,
    .2554073035717,
    -.16042841970921,
    .07132714986801,
    .13692232966423,
    .1064293757081,
    .19783779978752,
    .0621731877327,
    .27673536539078,
    .11514183133841,
    .12525399029255,
    .31533870100975,
    .15061867237091,
    .15787313878536,
    .24537208676338,
    .20615255832672,
    -.01037331111729,
    .13031965494156,
    .10124543309212,
    -.20607624948025,
    .1708429902792,
    -.10012608766556,
    -.05161434784532,
    -.04572645947337,
    -.04037792980671,
    .06448362022638,
    .14104247093201,
    .61098974943161,
    -.03648666664958,
    .96427005529404,
    .50965696573257,
    .60589480400085,
    .43643599748611,
    .9202772974968,
    .34723278880119,
    -.20308908820152,
    .23277981579304,
    .05809023976326,
    .07536666095257,
    -.52802640199661,
    .23601049184799,
    .1033476293087,
    -.00260917330161,
    .51983487606049,
    -.11813650280237,
    -.00874368380755,
    .29516834020615,
    .31515756249428,
    .67189866304398,
    .54028129577637,
    .29708743095398,
    1.0135440826416,
    1.095338344574,
    .74719160795212,
    1.3256005048752,
    1.2006633281708,
    -.11698111891747,
    .52947622537613,
    .81282931566238,
    .07182084023952,
    .68721032142639,
    .03820572793484,
    -.7624272108078,
    -.91694712638855,
    1.1456356048584,
    -1.2122505903244,
    -1.5362200737,
    -.14372782409191,
    -.23713812232018,
    -.2306577116251,
    .09432080388069,
    -.06653053313494,
    -.43669676780701,
    -.11725706607103,
    -.54083967208862,
    .28734645247459,
    -.467559248209,
    -.20749309659004,
    .39549562335014,
    -2.3498160839081,
    .3569367825985,
    -.15452179312706,
    .38143622875214,
    .35572397708893,
    .1091578528285,
    .24162948131561,
    .00510244909674,
    .24671010673046,
    .29062458872795,
    .34999752044678,
    .20432561635971,
    .80979299545288,
    .12853652238846,
    -.28230002522469,
    1.042493224144,
    .02615367434919,
    .36288577318192,
    1.5977079868317,
    -.38538381457329,
    -.93954759836197,
    .01978221163154,
    -.21140915155411,
    -.06908652186394,
    -.07760456204414,
    -.06621328741312,
    .0441059358418,
    -.07434900850058,
    -.14418575167656,
    -.40801778435707,
    .08931794017553,
    -.39096972346306,
    -.07610860466957,
    .19616261124611,
    -.07439963519573,
    .07448863238096,
    .25285106897354,
    -.30341354012489,
    -.07708399742842,
    .21437329053879,
    .34215462207794,
    -.22190742194653,
    .19737112522125,
    .12669529020786,
    -.58022564649582,
    -.3915441930294,
    .24621678888798,
    -.48660898208618,
    -.6255869269371,
    .28969877958298,
    -.18568041920662,
    -.00868325773627,
    .37611722946167,
    -.12999498844147,
    .5613916516304,
    .20089910924435,
    .6253759264946,
    .69802659749985,
    .00297940592282,
    .621961414814,
    -.42382326722145,
    -.01007199659944,
    -.84344571828842,
    -.71278285980225,
    .87191361188889,
    -.38806268572807,
    .32201409339905,
    .40175950527191,
    -.4677571952343,
    -.36986482143402,
    .38499811291695,
    .42079201340675,
    .04953457415104,
    .70659118890762,
    .53397834300995,
    -.20824746787548,
    .94835525751114,
    -.42157354950905,
    3.4305424690247,
    -1.8531830310822,
    .23821261525154,
    .71142077445984,
    -2.2795467376709,
    .93453133106232,
    1.0551145076752,
    -.08858433365822,
    .47923478484154,
    1.9373899698257,
    -.38597220182419,
    3.1213552951813,
    -4.0742712020874,
    -5.4928140640259,
    .77499634027481,
    1.0117527246475,
    .79578375816345])

yr = np.array([
    np.nan,
    .17000007629395,
    .08751478046179,
    -.12283346056938,
    .08991899341345,
    -.11544716358185,
    .12458966672421,
    -.03970721364021,
    -.13287504017353,
    .04697044193745,
    -.03501485288143,
    -.02120122499764,
    .09137260913849,
    -.09874293208122,
    .09212554246187,
    -.04526927694678,
    -.04142136126757,
    .13662992417812,
    -.0582881718874,
    .10619198530912,
    -.10801269859076,
    -.00338354869746,
    .01575009897351,
    .06893529742956,
    -.00832748971879,
    .10429482907057,
    -.05368844047189,
    .13637132942677,
    .26412883400917,
    -.02111134678125,
    .2554073035717,
    -.16042841970921,
    .07132714986801,
    .13692232966423,
    .1064293757081,
    .19783779978752,
    .0621731877327,
    .27673536539078,
    .11514183133841,
    .12525399029255,
    .31533870100975,
    .15061867237091,
    .15787313878536,
    .24537208676338,
    .20615255832672,
    -.01037331111729,
    .13031965494156,
    .10124543309212,
    -.20607624948025,
    .1708429902792,
    -.10012608766556,
    -.05161434784532,
    -.04572645947337,
    -.04037792980671,
    .06448362022638,
    .14104247093201,
    .61098974943161,
    -.03648666664958,
    .96427005529404,
    .50965696573257,
    .60589480400085,
    .43643599748611,
    .9202772974968,
    .34723278880119,
    -.20308908820152,
    .23277981579304,
    .05809023976326,
    .07536666095257,
    -.52802640199661,
    .23601049184799,
    .1033476293087,
    -.00260917330161,
    .51983487606049,
    -.11813650280237,
    -.00874368380755,
    .29516834020615,
    .31515756249428,
    .67189866304398,
    .54028129577637,
    .29708743095398,
    1.0135440826416,
    1.095338344574,
    .74719160795212,
    1.3256005048752,
    1.2006633281708,
    -.11698111891747,
    .52947622537613,
    .81282931566238,
    .07182084023952,
    .68721032142639,
    .03820572793484,
    -.7624272108078,
    -.91694712638855,
    1.1456356048584,
    -1.2122505903244,
    -1.5362200737,
    -.14372782409191,
    -.23713812232018,
    -.2306577116251,
    .09432080388069,
    -.06653053313494,
    -.43669676780701,
    -.11725706607103,
    -.54083967208862,
    .28734645247459,
    -.467559248209,
    -.20749309659004,
    .39549562335014,
    -2.3498160839081,
    .3569367825985,
    -.15452179312706,
    .38143622875214,
    .35572397708893,
    .1091578528285,
    .24162948131561,
    .00510244909674,
    .24671010673046,
    .29062458872795,
    .34999752044678,
    .20432561635971,
    .80979299545288,
    .12853652238846,
    -.28230002522469,
    1.042493224144,
    .02615367434919,
    .36288577318192,
    1.5977079868317,
    -.38538381457329,
    -.93954759836197,
    .01978221163154,
    -.21140915155411,
    -.06908652186394,
    -.07760456204414,
    -.06621328741312,
    .0441059358418,
    -.07434900850058,
    -.14418575167656,
    -.40801778435707,
    .08931794017553,
    -.39096972346306,
    -.07610860466957,
    .19616261124611,
    -.07439963519573,
    .07448863238096,
    .25285106897354,
    -.30341354012489,
    -.07708399742842,
    .21437329053879,
    .34215462207794,
    -.22190742194653,
    .19737112522125,
    .12669529020786,
    -.58022564649582,
    -.3915441930294,
    .24621678888798,
    -.48660898208618,
    -.6255869269371,
    .28969877958298,
    -.18568041920662,
    -.00868325773627,
    .37611722946167,
    -.12999498844147,
    .5613916516304,
    .20089910924435,
    .6253759264946,
    .69802659749985,
    .00297940592282,
    .621961414814,
    -.42382326722145,
    -.01007199659944,
    -.84344571828842,
    -.71278285980225,
    .87191361188889,
    -.38806268572807,
    .32201409339905,
    .40175950527191,
    -.4677571952343,
    -.36986482143402,
    .38499811291695,
    .42079201340675,
    .04953457415104,
    .70659118890762,
    .53397834300995,
    -.20824746787548,
    .94835525751114,
    -.42157354950905,
    3.4305424690247,
    -1.8531830310822,
    .23821261525154,
    .71142077445984,
    -2.2795467376709,
    .93453133106232,
    1.0551145076752,
    -.08858433365822,
    .47923478484154,
    1.9373899698257,
    -.38597220182419,
    3.1213552951813,
    -4.0742712020874,
    -5.4928140640259,
    .77499634027481,
    1.0117527246475,
    .79578375816345])

mse = np.array([
    1.4402351379395,
    1.4402351379395,
    .80966705083847,
    .74677377939224,
    .71241801977158,
    .69108927249908,
    .67678099870682,
    .66667699813843,
    .6592805981636,
    .6537224650383,
    .64946305751801,
    .64614951610565,
    .64354157447815,
    .64147007465363,
    .639812707901,
    .63847899436951,
    .63740062713623,
    .63652545213699,
    .63581293821335,
    .63523155450821,
    .63475602865219,
    .63436657190323,
    .63404709100723,
    .63378477096558,
    .63356912136078,
    .63339179754257,
    .63324582576752,
    .63312560319901,
    .63302659988403,
    .63294500112534,
    .63287770748138,
    .63282227516174,
    .63277649879456,
    .63273876905441,
    .63270765542984,
    .63268196582794,
    .63266080617905,
    .63264334201813,
    .63262891769409,
    .63261699676514,
    .63260716199875,
    .63259905576706,
    .63259238004684,
    .63258683681488,
    .63258230686188,
    .63257849216461,
    .63257539272308,
    .63257282972336,
    .63257074356079,
    .63256901502609,
    .63256752490997,
    .63256633281708,
    .63256537914276,
    .63256454467773,
    .63256388902664,
    .63256335258484,
    .63256287574768,
    .63256251811981,
    .63256222009659,
    .63256192207336,
    .63256174325943,
    .6325615644455,
    .63256138563156,
    .63256126642227,
    .63256120681763,
    .63256108760834,
    .63256102800369,
    .63256096839905,
    .63256096839905,
    .6325609087944,
    .63256084918976,
    .63256084918976,
    .63256084918976,
    .63256078958511,
    .63256078958511,
    .63256078958511,
    .63256078958511,
    .63256078958511,
    .63256078958511,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047,
    .63256072998047])

icstats = np.array([
    202,
    np.nan,
    -241.25977940638,
    4,
    490.51955881276,
    503.75262960236])


results = Bunch(
    llf=llf,
    nobs=nobs,
    k=k,
    k_exog=k_exog,
    sigma=sigma,
    chi2=chi2,
    df_model=df_model,
    k_ar=k_ar,
    k_ma=k_ma,
    params=params,
    cov_params=cov_params,
    xb=xb,
    y=y,
    resid=resid,
    yr=yr,
    mse=mse,
    icstats=icstats
)
