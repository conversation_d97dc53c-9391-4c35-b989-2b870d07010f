import numpy as np

from statsmodels.tools.testing import ParamsTableTestBunch

est = dict(
    mlag_var=3,
    fpe_var=7.47593408072e-13,
    aic_var=-19.4085401106207,
    hqic_var=-19.20760258859687,
    sbic_var=-18.91206199634062,
    tparms_var=30,
    k_var=30,
    df_eq_var=10,
    k_aux=18,
    k_eq=18,
    k_eq_var=3,
    k_dv_var=3,
    neqs_var=3,
    k_dv=3,
    neqs=3,
    N_cns=12,
    ic_ml=9,
    rc_ml=0,
    oid_df=0,
    N=199,
    rank=6,
    F_3_var=9.018236399703298,
    df_r3_var=189,
    df_m3_var=9,
    ll_3_var=364.1156201942387,
    r2_3_var=.3004252574875596,
    rmse_3_var=.0398398997327694,
    k_3_var=10,
    obs_3_var=199,
    F_2_var=5.002566602091567,
    df_r2_var=189,
    df_m2_var=9,
    ll_2_var=728.0001662442413,
    r2_2_var=.1923874161594955,
    rmse_2_var=.0064000343524738,
    k_2_var=10,
    obs_2_var=199,
    F_1_var=8.356742395485949,
    df_r1_var=189,
    df_m1_var=9,
    ll_1_var=694.4411801251371,
    r2_1_var=.2846617748967589,
    rmse_1_var=.0075756675969815,
    k_1_var=10,
    obs_1_var=199,
    df_r=193,
    df_r_var=189,
    ll=1945.759734821802,
    ll_var=1961.149741006759,
    detsig_ml_var=5.52855987611e-13,
    detsig_var=6.45335912865e-13,
    T_var=199,
    N_gaps_var=0,
    tmin=0,
    tmax=198,
    cmd="svar",
    cmdline="svar gdp cons inv, aeq(A) beq(B) lags(1/3) var dfk small",
    predict="svar_p",
    dfk_var="dfk",
    vcetype="EIM",
    lags_var="1 2 3",
    depvar_var="gdp cons inv",
    eqnames_var="gdp cons inv",
    endog_var="gdp cons inv",
    timevar="qtrdate",
    tsfmt="%tq",
    small="small",
    title="Structural vector autoregression",
    cns_b="[b_1_2]_cons = 0:[b_1_3]_cons = 0:[b_2_1]_cons = 0:[b_2_3]_cons = 0:[b_3_1]_cons = 0:[b_3_2]_cons = 0",  # noqa:E501
    cns_a="[a_1_1]_cons = 1:[a_1_2]_cons = 0:[a_1_3]_cons = 0:[a_2_2]_cons = 1:[a_2_3]_cons = 0:[a_3_3]_cons = 1",  # noqa:E501
    properties="b V",
)

params_table = np.array([
    1, np.nan, np.nan, np.nan,
    np.nan, np.nan,              193,  1.9723316757957,
    0, -.50680224519119,  .04791445158754, -10.577231469827,
    6.466439125e-21, -.60130543578568, -.41229905459671,              193,
    1.9723316757957,                0, -5.5360565201616,  .24220266982262,
    -22.857124259679,  8.232580974e-57,  -6.013760517815, -5.0583525225081,
    193,  1.9723316757957,                0,                0,
    np.nan, np.nan, np.nan, np.nan,
    np.nan,              193,  1.9723316757957,                0,
    1, np.nan, np.nan, np.nan,
    np.nan, np.nan,              193,  1.9723316757957,
    0,  3.0411768648574,  .28669329203947,  10.607771263929,
    5.260805180e-21,  2.4757226037298,   3.606631125985,              193,
    1.9723316757957,                0,                0, np.nan,
    np.nan, np.nan, np.nan, np.nan,
    193,  1.9723316757957,                0,                0,
    np.nan, np.nan, np.nan, np.nan,
    np.nan,              193,  1.9723316757957,                0,
    1, np.nan, np.nan, np.nan,
    np.nan, np.nan,              193,  1.9723316757957,
    0,  .00757566759698,  .00037973390425,   19.94993734326,
    8.739086225e-49,  .00682670638925,  .00832462880472,              193,
    1.9723316757957,                0,                0, np.nan,
    np.nan, np.nan, np.nan, np.nan,
    193,  1.9723316757957,                0,                0,
    np.nan, np.nan, np.nan, np.nan,
    np.nan,              193,  1.9723316757957,                0,
    0, np.nan, np.nan, np.nan,
    np.nan, np.nan,              193,  1.9723316757957,
    0,  .00512051886486,  .00025666841839,   19.94993734326,
    8.739086225e-49,  .00461428361309,  .00562675411662,              193,
    1.9723316757957,                0,                0, np.nan,
    np.nan, np.nan, np.nan, np.nan,
    193,  1.9723316757957,                0,                0,
    np.nan, np.nan, np.nan, np.nan,
    np.nan,              193,  1.9723316757957,                0,
    0, np.nan, np.nan, np.nan,
    np.nan, np.nan,              193,  1.9723316757957,
    0,  .02070894812762,  .00103804577284,   19.94993734326,
    8.739086225e-49,  .01866157756892,  .02275631868632,              193,
    1.9723316757957,                0]).reshape(18, 9)

params_table_colnames = 'b se t pvalue ll ul df crit eform'.split()

params_table_rownames = [
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons']

b = np.array([
    1, -.50680224519119, -5.5360565201616,                0,
    1,  3.0411768648574,                0,                0,
    1,  .00757566759698,                0,                0,
    0,  .00512051886486,                0,                0,
    0,  .02070894812762])

b_colnames = [
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons']

b_rownames = 'y1'.split()

cov = np.array([
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,  .00229579467093,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,   .0586621332692,                0,
    0, -.04165561908647,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    -.04165561908647,                0,                0,  .08219304370043,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,  1.441978380e-07,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,  6.587867700e-08,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,  1.077539027e-06
    ]).reshape(18, 18)

cov_colnames = [
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons']

cov_rownames = [
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons']

constraints = np.array([
    1,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                1,                0,
    0,                0,                1,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    1,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                1,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                1,
    0,                0,                0,                0,
    0,                0,                0,                1,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                1,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                1,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                1,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    1,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                1,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                1,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                1,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                0,
    0,                0,                0,                1,
    0,                0,                0,                0
    ]).reshape(12, 19)

constraints_colnames = [
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons', '_cons', '_cons', '_cons',
    '_cons', '_cons', '_cons', '_cons', '_r']

constraints_rownames = 'r1 r2 r3 r4 r5 r6 r7 r8 r9 r10 r11 r12'.split()

Sigma = np.array([
    .00005739073954,  .00002908575565,  .00022926345064,  .00002908575565,
    .00004096043971,   .0000364524456,  .00022926345064,   .0000364524456,
    .00158721761072]).reshape(3, 3)

Sigma_colnames = 'gdp cons inv'.split()

Sigma_rownames = 'gdp cons inv'.split()

G_var = np.array([
    1,  .00772119249309,  .00832845872247,  .00812414768988,
    .00772450051084,  .00839168407728,  .00810118500591,  .00793331513676,
    .00846090823295,    .009386666817,  .00772119249309,  .00013590038218,
    .00010436399127,  .00039355021854,  .00008395547668,  .00009296949447,
    .0001468047815,  .00007985818625,  .00008622263703,  .00012491817455,
    .00832845872247,  .00010436399127,   .0001177915254,   .0001572415776,
    .00008140583018,  .00008416323485,  .00014479739125,  .00007884622137,
    .0000839417926,  .00012879456896,  .00812414768988,  .00039355021854,
    .0001572415776,  .00222357091844,   .0001852293649,  .00023227850984,
    .00042852108282,  .00014155595459,  .00015686829612,  .00027431611677,
    .00772450051084,  .00008395547668,  .00008140583018,   .0001852293649,
    .00013589031191,  .00010428130248,  .00039335411738,  .00008365141811,
    .00009289191013,   .0001446903813,  .00839168407728,  .00009296949447,
    .00008416323485,  .00023227850984,  .00010428130248,    .000118309348,
    .00015273148978,  .00008252427592,  .00008497769731,  .00014704611828,
    .00810118500591,   .0001468047815,  .00014479739125,  .00042852108282,
    .00039335411738,  .00015273148978,  .00222677863348,  .00017054880362,
    .0002272506141,  .00033983014403,  .00793331513676,  .00007985818625,
    .00007884622137,  .00014155595459,  .00008365141811,  .00008252427592,
    .00017054880362,  .00013762976929,  .00010632331501,  .00038874930061,
    .00846090823295,  .00008622263703,   .0000839417926,  .00015686829612,
    .00009289191013,  .00008497769731,   .0002272506141,  .00010632331501,
    .000119472079,  .00016022586575,    .009386666817,  .00012491817455,
    .00012879456896,  .00027431611677,   .0001446903813,  .00014704611828,
    .00033983014403,  .00038874930061,  .00016022586575,  .00210416228523
    ]).reshape(10, 10)

G_var_colnames = [
    '_cons',
    'L.gdp', 'L.cons', 'L.inv',
    'L2.gdp', 'L2.cons', 'L2.inv',
    'L3.gdp', 'L3.cons', 'L3.inv']

G_var_rownames = [
    '_cons',
    'L.gdp', 'L.cons', 'L.inv',
    'L2.gdp', 'L2.cons', 'L2.inv',
    'L3.gdp', 'L3.cons', 'L3.inv']


bf_var = np.array([
    -.28614799058891,  .02569110476595, -.18003096181942,   .6738689560015,
    .29544106895159,  .18370240194258,  .03057777928182, -.01444291994803,
    .01263245201514,  .00128149319157, -.12715587337617, -.08663431448056,
    -.35906668730993,  .25639388994688,  .20570668527827,  .41845237867104,
    .02404284475263,  .00384555072972,  .04190581088286,  .00483719365525,
    -1.8625374877103,  .33142498594011, -.48831009148236,  4.4033743272466,
    .87819807698004, -.12378698529172,  .22371717935155, -.09655522236577,
    .03345298758638, -.02059735685585])

bf_var_colnames = [
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons',
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons',
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons']

bf_var_rownames = 'r1'.split()

B = np.array([
    .00757566759698,                0,                0,                0,
    .00512051886486,                0,                0,                0,
    .02070894812762]).reshape(3, 3)

B_colnames = 'gdp cons inv'.split()

B_rownames = 'gdp cons inv'.split()

A = np.array([
    1,                0,                0, -.50680224519119,
    1,                0, -5.5360565201616,  3.0411768648574,
    1]).reshape(3, 3)

A_colnames = 'gdp cons inv'.split()

A_rownames = 'gdp cons inv'.split()

beq = np.array([
    np.nan,                0,                0,                0,
    np.nan,                0,                0,                0,
    np.nan]).reshape(3, 3)

beq_colnames = 'c1 c2 c3'.split()

beq_rownames = 'r1 r2 r3'.split()

aeq = np.array([
    1,                0,                0, np.nan,
    1,                0, np.nan, np.nan,
    1]).reshape(3, 3)

aeq_colnames = 'c1 c2 c3'.split()

aeq_rownames = 'r1 r2 r3'.split()

V_var = np.array([
    .02944043907167, -.00139167936464,  -.0010606099932, -.01749947940302,
    .00202095994301, -.00138072504574, -.00385176007523,  .00038731129816,
    .00020334459451, -.00004143863419,  .01492048062093, -.00070530622659,
    -.00053751952583, -.00886877545113,  .00102422703656, -.00069975455317,
    -.00195208065406,   .0001962902355,  .00010305549704, -.00002100119285,
    .11760811420253, -.00555945464168, -.00423690492187, -.06990659232715,
    .00807329290157, -.00551569453385, -.01538693895515,  .00154722391453,
    .00081231717487, -.00016553827919, -.00139167936464,  .03044262457881,
    -.00102187006012,  .00135423549927, -.01978648635158,  .00141733933507,
    .0005786735915, -.00404788575193,   .0001576008945, -.00004691846312,
    -.00070530622659,  .01542839048605, -.00051788604076,  .00068632959155,
    -.01002783570742,  .00071831075721,   .0002932730754, -.00205147758736,
    .00007987248718, -.00002377838245, -.00555945464168,  .12161162608255,
    -.0040821473633,  .00540987459008, -.07904268481996,  .00566196061062,
    .00231167441725, -.01617041813247,  .00062958109943,  -.0001874289971,
    -.0010606099932, -.00102187006012,   .0305750957042,  .00161206604309,
    .00123567563375, -.01979131075828,  .00006003651469,  .00052765232747,
    -.00406616514879,  -.0000494168379, -.00053751952583, -.00051788604076,
    .01549552714982,  .00081699869003,  .00062624318551, -.01003028072757,
    .00003042664044,  .00026741538424, -.00206074162672,  -.0000250445644,
    -.00423690492187,  -.0040821473633,  .12214081925136,  .00643985121405,
    .00493625386149, -.07906195726942,  .00023983274364,    .002107855628,
    -.01624344032441, -.00019740945784, -.01749947940302,  .00135423549927,
    .00161206604309,   .0174886287578, -.00252569799616, -.00071586401207,
    .00214091625575, -.00039776436038, -.00032773904917, -.00001762895502,
    -.00886877545113,  .00068632959155,  .00081699869003,  .00886327631977,
    -.00128002941513, -.00036280148857,  .00108502116518,  -.0002015878709,
    -.00016609888596, -8.934393985e-06, -.06990659232715,  .00540987459008,
    .00643985121405,  .06986324637298,   -.010089611016, -.00285972013799,
    .00855249213135, -.00158898161155, -.00130924581082,  -.0000704238191,
    .00202095994301, -.01978648635158,  .00123567563375, -.00252569799616,
    .02190111225253, -.00177986396497, -.00110297152268,  .00248014965403,
    -.00035987053166, -.00002801274167,  .00102422703656, -.01002783570742,
    .00062624318551, -.00128002941513,  .01109953286177, -.00090203905358,
    -.00055898844408,  .00125694541307, -.00018238319342, -.00001419692037,
    .00807329290157, -.07904268481996,  .00493625386149,   -.010089611016,
    .08749015273475, -.00711016720733, -.00440612996585,  .00990765535257,
    -.00143760405483, -.00011190477537, -.00138072504574,  .00141733933507,
    -.01979131075828, -.00071586401207, -.00177986396497,  .02191808721673,
    .00005751246451, -.00100612185989,   .0023961694647, -.00002263401879,
    -.00069975455317,  .00071831075721, -.01003028072757, -.00036280148857,
    -.00090203905358,  .01110813581173,  .00002914744614, -.00050990481753,
    .00121438406457, -.00001147097154, -.00551569453385,  .00566196061062,
    -.07906195726942, -.00285972013799, -.00711016720733,  .08755796400357,
    .00022974971529,  -.0040192367482,  .00957217286629, -.00009041795403,
    -.00385176007523,   .0005786735915,  .00006003651469,  .00214091625575,
    -.00110297152268,  .00005751246451,   .0006984186117, -.00009557423262,
    -4.697657444e-06,  .00001087688008, -.00195208065406,   .0002932730754,
    .00003042664044,  .00108502116518, -.00055898844408,  .00002914744614,
    .00035396012049, -.00004843723567, -2.380783340e-06,  5.512427248e-06,
    -.01538693895515,  .00231167441725,  .00023983274364,  .00855249213135,
    -.00440612996585,  .00022974971529,  .00279002957959, -.00038179815311,
    -.00001876611391,   .0000434507567,  .00038731129816, -.00404788575193,
    .00052765232747, -.00039776436038,  .00248014965403, -.00100612185989,
    -.00009557423262,   .0007270051133, -.00006681356415,  .00001061820762,
    .0001962902355, -.00205147758736,  .00026741538424,  -.0002015878709,
    .00125694541307, -.00050990481753, -.00004843723567,  .00036844782368,
    -.00003386126432,  5.381331462e-06,  .00154722391453, -.01617041813247,
    .002107855628, -.00158898161155,  .00990765535257,  -.0040192367482,
    -.00038179815311,  .00290422640037, -.00026690557379,  .00004241741679,
    .00020334459451,   .0001576008945, -.00406616514879, -.00032773904917,
    -.00035987053166,   .0023961694647, -4.697657444e-06, -.00006681356415,
    .00069956889753,  8.959242929e-06,  .00010305549704,  .00007987248718,
    -.00206074162672, -.00016609888596, -.00018238319342,  .00121438406457,
    -2.380783340e-06, -.00003386126432,  .00035454308793,  4.540564432e-06,
    .00081231717487,  .00062958109943, -.01624344032441, -.00130924581082,
    -.00143760405483,  .00957217286629, -.00001876611391, -.00026690557379,
    .00279462471985,  .00003579021573, -.00004143863419, -.00004691846312,
    -.0000494168379, -.00001762895502, -.00002801274167, -.00002263401879,
    .00001087688008,  .00001061820762,  8.959242929e-06,  1.677729973e-06,
    -.00002100119285, -.00002377838245,  -.0000250445644, -8.934393985e-06,
    -.00001419692037, -.00001147097154,  5.512427248e-06,  5.381331462e-06,
    4.540564432e-06,  8.502773173e-07, -.00016553827919,  -.0001874289971,
    -.00019740945784,  -.0000704238191, -.00011190477537, -.00009041795403,
    .0000434507567,  .00004241741679,  .00003579021573,  6.702164252e-06,
    .01492048062093, -.00070530622659, -.00053751952583, -.00886877545113,
    .00102422703656, -.00069975455317, -.00195208065406,   .0001962902355,
    .00010305549704, -.00002100119285,    .021011984501, -.00099325778291,
    -.00075696971382, -.01248958241067,  .00144238266611, -.00098543955783,
    -.0027490460589,   .0002764285877,  .00014512940713,  -.0000295752362,
    .01869946288115, -.00088394254441, -.00067365969478,  -.0111150130859,
    .00128363796975, -.00087698477183, -.00244649355869,  .00024600561241,
    .00012915686101, -.00002632026649, -.00070530622659,  .01542839048605,
    -.00051788604076,  .00068632959155, -.01002783570742,  .00071831075721,
    .0002932730754, -.00205147758736,  .00007987248718, -.00002377838245,
    -.00099325778291,  .02172725597817, -.00072932057206,  .00096653365977,
    -.01412184592548,  .00101157160288,  .00041300608683, -.00288902324026,
    .00011248159528, -.00003348625398, -.00088394254441,  .01933601353331,
    -.00064905354204,  .00086015960527, -.01256763413681,  .00090024079538,
    .00036755176504, -.00257106523382,  .00010010217815, -.00002980084833,
    -.00053751952583, -.00051788604076,  .01549552714982,  .00081699869003,
    .00062624318551, -.01003028072757,  .00003042664044,  .00026741538424,
    -.00206074162672,  -.0000250445644, -.00075696971382, -.00072932057206,
    .02182180216435,  .00115055032398,  .00088191610191, -.01412528916077,
    .00004284876028,  .00037659161603, -.00290206945884, -.00003526937318,
    -.00067365969478, -.00064905354204,  .01942015422449,  .00102392389806,
    .00078485482469, -.01257069841903,  .00003813294276,  .00033514497143,
    -.00258267562122,  -.0000313877223, -.00886877545113,  .00068632959155,
    .00081699869003,  .00886327631977, -.00128002941513, -.00036280148857,
    .00108502116518,  -.0002015878709, -.00016609888596, -8.934393985e-06,
    -.01248958241067,  .00096653365977,  .00115055032398,  .01248183817871,
    -.00180262009749, -.00051092048898,  .00152799688463, -.00028388906004,
    -.00023391117927, -.00001258199067,  -.0111150130859,  .00086015960527,
    .00102392389806,  .01110812116296, -.00160422865342, -.00045468997555,
    .00135982972124, -.00025264500554, -.00020816755381, -.00001119725114,
    .00102422703656, -.01002783570742,  .00062624318551, -.00128002941513,
    .01109953286177, -.00090203905358, -.00055898844408,  .00125694541307,
    -.00018238319342, -.00001419692037,  .00144238266611, -.01412184592548,
    .00088191610191, -.00180262009749,  .01563107907748, -.00127030965655,
    -.00078720363114,  .00177011171483, -.00025684379282, -.00001999302022,
    .00128363796975, -.01256763413681,  .00078485482469, -.00160422865342,
    .01391076521058, -.00113050284561, -.00070056614975,  .00157529805457,
    -.00022857626656, -.00001779264303, -.00069975455317,  .00071831075721,
    -.01003028072757, -.00036280148857, -.00090203905358,  .01110813581173,
    .00002914744614, -.00050990481753,  .00121438406457, -.00001147097154,
    -.00098543955783,  .00101157160288, -.01412528916077, -.00051092048898,
    -.00127030965655,  .01564319430727,  .00004104731625,   -.000718080898,
    .0017101740749, -.00001615416302, -.00087698477183,  .00090024079538,
    -.01257069841903, -.00045468997555, -.00113050284561,  .01392154707127,
    .00003652976074, -.00063905087581,  .00152195698757, -.00001437627996,
    -.00195208065406,   .0002932730754,  .00003042664044,  .00108502116518,
    -.00055898844408,  .00002914744614,  .00035396012049, -.00004843723567,
    -2.380783340e-06,  5.512427248e-06,  -.0027490460589,  .00041300608683,
    .00004284876028,  .00152799688463, -.00078720363114,  .00004104731625,
    .00049846950341, -.00006821244376, -3.352772870e-06,  7.762956089e-06,
    -.00244649355869,  .00036755176504,  .00003813294276,  .00135982972124,
    -.00070056614975,  .00003652976074,  .00044360931144, -.00006070516852,
    -2.983775846e-06,  6.908586346e-06,   .0001962902355, -.00205147758736,
    .00026741538424,  -.0002015878709,  .00125694541307, -.00050990481753,
    -.00004843723567,  .00036844782368, -.00003386126432,  5.381331462e-06,
    .0002764285877, -.00288902324026,  .00037659161603, -.00028388906004,
    .00177011171483,   -.000718080898, -.00006821244376,  .00051887202278,
    -.00004768561946,  7.578338537e-06,  .00024600561241, -.00257106523382,
    .00033514497143, -.00025264500554,  .00157529805457, -.00063905087581,
    -.00006070516852,  .00046176638526, -.00004243747044,  6.744287298e-06,
    .00010305549704,  .00007987248718, -.00206074162672, -.00016609888596,
    -.00018238319342,  .00121438406457, -2.380783340e-06, -.00003386126432,
    .00035454308793,  4.540564432e-06,  .00014512940713,  .00011248159528,
    -.00290206945884, -.00023391117927, -.00025684379282,   .0017101740749,
    -3.352772870e-06, -.00004768561946,  .00049929047581,  6.394316100e-06,
    .00012915686101,  .00010010217815, -.00258267562122, -.00020816755381,
    -.00022857626656,  .00152195698757, -2.983775846e-06, -.00004243747044,
    .00044433992986,  5.690575137e-06, -.00002100119285, -.00002377838245,
    -.0000250445644, -8.934393985e-06, -.00001419692037, -.00001147097154,
    5.512427248e-06,  5.381331462e-06,  4.540564432e-06,  8.502773173e-07,
    -.0000295752362, -.00003348625398, -.00003526937318, -.00001258199067,
    -.00001999302022, -.00001615416302,  7.762956089e-06,  7.578338537e-06,
    6.394316100e-06,  1.197415436e-06, -.00002632026649, -.00002980084833,
    -.0000313877223, -.00001119725114, -.00001779264303, -.00001437627996,
    6.908586346e-06,  6.744287298e-06,  5.690575137e-06,  1.065631164e-06,
    .11760811420253, -.00555945464168, -.00423690492187, -.06990659232715,
    .00807329290157, -.00551569453385, -.01538693895515,  .00154722391453,
    .00081231717487, -.00016553827919,  .01869946288115, -.00088394254441,
    -.00067365969478,  -.0111150130859,  .00128363796975, -.00087698477183,
    -.00244649355869,  .00024600561241,  .00012915686101, -.00002632026649,
    .81421469275997, -.03848875295449, -.02933258697842, -.48397149277921,
    .0558923484488, -.03818579662369,  -.1065255731616,  .01071161163275,
    .00562376655253, -.00114604081571, -.00555945464168,  .12161162608255,
    -.0040821473633,  .00540987459008, -.07904268481996,  .00566196061062,
    .00231167441725, -.01617041813247,  .00062958109943,  -.0001874289971,
    -.00088394254441,  .01933601353331, -.00064905354204,  .00086015960527,
    -.01256763413681,  .00090024079538,  .00036755176504, -.00257106523382,
    .00010010217815, -.00002980084833, -.03848875295449,  .84193147248604,
    -.02826118234912,  .03745319280989, -.54722172676604,  .03919841373406,
    .01600399163071,  -.1119496908934,  .00435866338746, -.00129759280914,
    -.00423690492187,  -.0040821473633,  .12214081925136,  .00643985121405,
    .00493625386149, -.07906195726942,  .00023983274364,    .002107855628,
    -.01624344032441, -.00019740945784, -.00067365969478, -.00064905354204,
    .01942015422449,  .00102392389806,  .00078485482469, -.01257069841903,
    .00003813294276,  .00033514497143, -.00258267562122,  -.0000313877223,
    -.02933258697842, -.02826118234912,  .84559513852024,   .0445838411169,
    .03417426126141, -.54735515218165,  .00166039005894,  .01459293037875,
    -.11245523204569, -.00136668870299, -.06990659232715,  .00540987459008,
    .00643985121405,  .06986324637298,   -.010089611016, -.00285972013799,
    .00855249213135, -.00158898161155, -.00130924581082,  -.0000704238191,
    -.0111150130859,  .00086015960527,  .00102392389806,  .01110812116296,
    -.00160422865342, -.00045468997555,  .00135982972124, -.00025264500554,
    -.00020816755381, -.00001119725114, -.48397149277921,  .03745319280989,
    .0445838411169,  .48367140368251, -.06985155394389, -.01979817608096,
    .05920990061166, -.01100070503994, -.00906406146236, -.00048755231406,
    .00807329290157, -.07904268481996,  .00493625386149,   -.010089611016,
    .08749015273475, -.00711016720733, -.00440612996585,  .00990765535257,
    -.00143760405483, -.00011190477537,  .00128363796975, -.01256763413681,
    .00078485482469, -.00160422865342,  .01391076521058, -.00113050284561,
    -.00070056614975,  .00157529805457, -.00022857626656, -.00001779264303,
    .0558923484488, -.54722172676604,  .03417426126141, -.06985155394389,
    .60570453247617, -.04922451692591, -.03050415169677,  .06859185366219,
    -.00995270055771, -.00077472981275, -.00551569453385,  .00566196061062,
    -.07906195726942, -.00285972013799, -.00711016720733,  .08755796400357,
    .00022974971529,  -.0040192367482,  .00957217286629, -.00009041795403,
    -.00087698477183,  .00090024079538, -.01257069841903, -.00045468997555,
    -.00113050284561,  .01392154707127,  .00003652976074, -.00063905087581,
    .00152195698757, -.00001437627996, -.03818579662369,  .03919841373406,
    -.54735515218165, -.01979817608096, -.04922451692591,  .60617399779988,
    .00159058407757, -.02782564482269,  .06626926927807, -.00062597404237,
    -.01538693895515,  .00231167441725,  .00023983274364,  .00855249213135,
    -.00440612996585,  .00022974971529,  .00279002957959, -.00038179815311,
    -.00001876611391,   .0000434507567, -.00244649355869,  .00036755176504,
    .00003813294276,  .00135982972124, -.00070056614975,  .00003652976074,
    .00044360931144, -.00006070516852, -2.983775846e-06,  6.908586346e-06,
    -.1065255731616,  .01600399163071,  .00166039005894,  .05920990061166,
    -.03050415169677,  .00159058407757,  .01931570021627, -.00264323314799,
    -.00012991999552,  .00030081465683,  .00154722391453, -.01617041813247,
    .002107855628, -.00158898161155,  .00990765535257,  -.0040192367482,
    -.00038179815311,  .00290422640037, -.00026690557379,  .00004241741679,
    .00024600561241, -.00257106523382,  .00033514497143, -.00025264500554,
    .00157529805457, -.00063905087581, -.00006070516852,  .00046176638526,
    -.00004243747044,  6.744287298e-06,  .01071161163275,  -.1119496908934,
    .01459293037875, -.01100070503994,  .06859185366219, -.02782564482269,
    -.00264323314799,  .02010629812676, -.00184781841992,  .00029366072412,
    .00081231717487,  .00062958109943, -.01624344032441, -.00130924581082,
    -.00143760405483,  .00957217286629, -.00001876611391, -.00026690557379,
    .00279462471985,  .00003579021573,  .00012915686101,  .00010010217815,
    -.00258267562122, -.00020816755381, -.00022857626656,  .00152195698757,
    -2.983775846e-06, -.00004243747044,  .00044433992986,  5.690575137e-06,
    .00562376655253,  .00435866338746, -.11245523204569, -.00906406146236,
    -.00995270055771,  .06626926927807, -.00012991999552, -.00184781841992,
    .01934751290831,  .00024777983816, -.00016553827919,  -.0001874289971,
    -.00019740945784,  -.0000704238191, -.00011190477537, -.00009041795403,
    .0000434507567,  .00004241741679,  .00003579021573,  6.702164252e-06,
    -.00002632026649, -.00002980084833,  -.0000313877223, -.00001119725114,
    -.00001779264303, -.00001437627996,  6.908586346e-06,  6.744287298e-06,
    5.690575137e-06,  1.065631164e-06, -.00114604081571, -.00129759280914,
    -.00136668870299, -.00048755231406, -.00077472981275, -.00062597404237,
    .00030081465683,  .00029366072412,  .00024777983816,  .00004639986487
    ]).reshape(30, 30)

V_var_colnames = [
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons',
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons',
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons']

V_var_rownames = [
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons',
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons',
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons']

b_var = np.array([
    -.28614799058891,  .02569110476595, -.18003096181942,   .6738689560015,
    .29544106895159,  .18370240194258,  .03057777928182, -.01444291994803,
    .01263245201514,  .00128149319157, -.12715587337617, -.08663431448056,
    -.35906668730993,  .25639388994688,  .20570668527827,  .41845237867104,
    .02404284475263,  .00384555072972,  .04190581088286,  .00483719365525,
    -1.8625374877103,  .33142498594011, -.48831009148236,  4.4033743272466,
    .87819807698004, -.12378698529172,  .22371717935155, -.09655522236577,
    .03345298758638, -.02059735685585])

b_var_colnames = [
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons',
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons',
    'L.gdp', 'L2.gdp', 'L3.gdp',
    'L.cons', 'L2.cons', 'L3.cons',
    'L.inv', 'L2.inv', 'L3.inv',
    '_cons']

b_var_rownames = 'y1'.split()


results_svar1_small = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    b=b,
    b_colnames=b_colnames,
    b_rownames=b_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    constraints=constraints,
    constraints_colnames=constraints_colnames,
    constraints_rownames=constraints_rownames,
    Sigma=Sigma,
    Sigma_colnames=Sigma_colnames,
    Sigma_rownames=Sigma_rownames,
    G_var=G_var,
    G_var_colnames=G_var_colnames,
    G_var_rownames=G_var_rownames,
    bf_var=bf_var,
    bf_var_colnames=bf_var_colnames,
    bf_var_rownames=bf_var_rownames,
    B=B,
    B_colnames=B_colnames,
    B_rownames=B_rownames,
    A=A,
    A_colnames=A_colnames,
    A_rownames=A_rownames,
    beq=beq,
    beq_colnames=beq_colnames,
    beq_rownames=beq_rownames,
    aeq=aeq,
    aeq_colnames=aeq_colnames,
    aeq_rownames=aeq_rownames,
    V_var=V_var,
    V_var_colnames=V_var_colnames,
    V_var_rownames=V_var_rownames,
    b_var=b_var,
    b_var_colnames=b_var_colnames,
    b_var_rownames=b_var_rownames,
    **est
)


results_svar1_small.__doc__ = """
    Scalars
      e(N)                number of observations
      e(N_cns)            number of constraints
      e(k_eq)             number of equations in e(b)
      e(k_dv)             number of dependent variables
      e(k_aux)            number of auxiliary parameters
      e(ll)               log likelihood from svar
      e(ll_#)             log likelihood for equation #
      e(N_gaps_var)       number of gaps in the sample
      e(k_var)            number of coefficients in VAR
      e(k_eq_var)         number of equations in underlying VAR
      e(k_dv_var)         number of dependent variables in underlying VAR
      e(df_eq_var)        average number of parameters in an equation
      e(df_m_var)         model degrees of freedom
      e(df_r_var)         if small, residual degrees of freedom
      e(obs_#_var)        number of observations on equation #
      e(k_#_var)          number of coefficients in equation #
      e(df_m#_var)        model degrees of freedom for equation #
      e(df_r#_var)        residual degrees of freedom for equation # (small only)
      e(r2_#_var)         R-squared for equation #
      e(ll_#_var)         log likelihood for equation # VAR
      e(chi2_#_var)       chi-squared statistic for equation #
      e(F_#_var)          F statistic for equation # (small only)
      e(rmse_#_var)       root mean squared error for equation #
      e(mlag_var)         highest lag in VAR
      e(tparms_var)       number of parameters in all equations
      e(aic_var)          Akaike information criterion
      e(hqic_var)         Hannan-Quinn information criterion
      e(sbic_var)         Schwarz-Bayesian information criterion
      e(fpe_var)          final prediction error
      e(ll_var)           log likelihood from var
      e(detsig_var)       determinant of e(Sigma)
      e(detsig_ml_var)    determinant of Sigma_ml hat
      e(tmin)             first time period in the sample
      e(tmax)             maximum time
      e(chi2_oid)         overidentification test
      e(oid_df)           number of overidentifying restrictions
      e(rank)             rank of e(V)
      e(ic_ml)            number of iterations
      e(rc_ml)            return code from ml

    Matrices
      e(b)                coefficient vector
      e(Cns)              constraints matrix
      e(Sigma)            Sigma hat matrix
      e(V)                variance-covariance matrix of the estimators
      e(b_var)            coefficient vector of underlying VAR model
      e(V_var)            VCE of underlying VAR model
      e(bf_var)           full coefficient vector with zeros in dropped lags
      e(G_var)            Gamma matrix saved by var; see Methods and formulas in [TS] var svar
      e(aeq)              aeq(matrix), if specified
      e(acns)             acns(matrix), if specified
      e(beq)              beq(matrix), if specified
      e(bcns)             bcns(matrix), if specified
      e(lreq)             lreq(matrix), if specified
      e(lrcns)            lrcns(matrix), if specified
      e(Cns_var)          constraint matrix from var, if varconstraints() is specified
      e(A)                estimated A matrix, if a short-run model
      e(B)                estimated B matrix
      e(C)                estimated C matrix, if a long-run model
      e(A1)               estimated A bar matrix, if a long-run model
"""  # noqa:E501
