#!/usr/bin/env python3
"""
测试pytdx数据获取限制
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from backtest.data.pytdx_client import TdxDataClient
import pandas as pd

def test_pytdx_data_limit():
    """测试pytdx数据获取限制"""
    print("🔍 测试pytdx数据获取限制...")
    
    try:
        client = TdxDataClient()
        
        # 测试获取大量1分钟数据
        print("测试获取518880基金的1分钟数据...")
        
        # 尝试获取不同数量的数据
        test_counts = [1000, 5000, 10000, 15000, 20000]
        
        for count in test_counts:
            print(f"\n尝试获取 {count} 条记录...")
            try:
                data = client.get_security_bars(
                    symbol='518880',
                    frequency='1min',
                    count=count
                )
                print(f"实际获取到: {len(data)} 条记录")
                if len(data) > 0:
                    print(f"时间范围: {data.index.min()} 至 {data.index.max()}")
                
                # 如果获取的数据少于请求的数量，可能遇到了限制
                if len(data) < count:
                    print(f"⚠️  可能遇到数据限制: 请求{count}条，实际获取{len(data)}条")
                    break
                    
            except Exception as e:
                print(f"❌ 获取失败: {e}")
                break
        
        # 测试使用日期范围获取数据
        print(f"\n测试使用日期范围获取数据...")
        try:
            data_with_dates = client.get_security_bars(
                symbol='518880',
                frequency='1min',
                start_date='2024-01-01',
                end_date='2024-12-31'
            )
            print(f"使用日期范围获取到: {len(data_with_dates)} 条记录")
            if len(data_with_dates) > 0:
                print(f"时间范围: {data_with_dates.index.min()} 至 {data_with_dates.index.max()}")
        except Exception as e:
            print(f"❌ 使用日期范围获取失败: {e}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_pytdx_data_limit()
