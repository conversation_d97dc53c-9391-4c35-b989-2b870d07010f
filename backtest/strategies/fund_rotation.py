"""
基金轮动策略 - 基于rolling_pair.py逻辑的两基金动态切换策略
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple, Union
from ..core.strategy import BaseStrategy
import logging
logger = logging.getLogger(__name__)
from statsmodels.regression.rolling import RollingOLS
STATSMODELS_AVAILABLE = True



class FundRotationStrategy(BaseStrategy):
    """
    基金轮动策略
    
    基于rolling_pair.py的逻辑，实现两个基金之间的动态切换交易策略。
    使用滚动回归分析两个基金的相对价格关系，当价格比率偏离正常区间时进行轮动。
    """
    
    def __init__(self, 
                 fund_pair: Tuple[str, str],
                 window: int = 60,
                 std_dev_mult: float = 2.0,
                 commission_rate: float = 0.001,
                 slippage_rate: float = 0.001,
                 position_size: float = 1.0,
                 name: str = "基金轮动策略",
                 **kwargs):
        """
        初始化基金轮动策略
        
        Args:
            fund_pair: 基金对，格式为(fund1_code, fund2_code)
            window: 滚动回归窗口大小
            std_dev_mult: 标准差倍数，用于确定交易区间
            commission_rate: 佣金率
            slippage_rate: 滑点率
            position_size: 仓位大小
            name: 策略名称
            **kwargs: 其他参数
        """
        if not STATSMODELS_AVAILABLE:
            raise ImportError("需要安装statsmodels库: pip install statsmodels")
        
        params = {
            'fund_pair': fund_pair,
            'window': window,
            'std_dev_mult': std_dev_mult,
            'commission_rate': commission_rate,
            'slippage_rate': slippage_rate,
            'position_size': position_size,
            **kwargs
        }
        super().__init__(name=name, params=params)
        
        self.fund1, self.fund2 = fund_pair
        self.signals_df = None
        self.current_position = 0  # 0: 空仓, 1: 持有fund1, -1: 持有fund2
        self.price_history = None  # 初始化价格历史
    
    def set_data(self, data: Union[Dict[str, pd.DataFrame], pd.DataFrame]) -> None:
        """
        设置市场数据

        Args:
            data: 包含两个基金数据的字典，格式为{fund_code: DataFrame}，或单个DataFrame
        """
        if isinstance(data, dict):
            # 字典格式数据
            if self.fund1 not in data or self.fund2 not in data:
                raise ValueError(f"数据中缺少基金 {self.fund1} 或 {self.fund2}")

            # 合并两个基金的价格数据
            fund1_data = data[self.fund1]['close'].rename(self.fund1)
            fund2_data = data[self.fund2]['close'].rename(self.fund2)

            # 创建价格历史DataFrame
            self.price_history = pd.concat([fund1_data, fund2_data], axis=1).dropna()

            if self.price_history.empty:
                raise ValueError("合并后的价格数据为空")

            # 调用父类方法设置数据（使用fund1的完整数据作为主数据）
            super().set_data(data[self.fund1])
        else:
            # 单个DataFrame格式数据（用于兼容标准回测引擎）
            # 在这种情况下，我们需要从外部设置价格历史
            if data is not None and not data.empty:
                super().set_data(data)

                # 如果没有价格历史，创建一个简单的价格历史用于演示
                if not hasattr(self, 'price_history') or self.price_history is None:
                    logger.warning("基金轮动策略缺少配对数据，使用单一数据源进行演示")
                    # 创建模拟的第二个基金数据
                    fund1_prices = data['close']
                    # 简单的相关数据：添加一些噪声和趋势差异
                    np.random.seed(42)
                    fund2_prices = fund1_prices * (0.8 + 0.4 * np.random.random(len(fund1_prices)))
                    fund2_prices = fund2_prices + np.random.normal(0, fund1_prices.std() * 0.1, len(fund1_prices))

                    self.price_history = pd.DataFrame({
                        self.fund1: fund1_prices,
                        self.fund2: fund2_prices
                    }, index=data.index).dropna()
            else:
                raise ValueError("传入的数据为空或None")

        # 预处理数据
        self.prepare_data()

    def set_fund_data(self, fund_data: Dict[str, pd.DataFrame]) -> None:
        """
        专门用于设置基金配对数据的方法

        Args:
            fund_data: 包含两个基金数据的字典
        """
        self.set_data(fund_data)
    
    def prepare_data(self) -> None:
        """
        预处理数据，计算交易信号
        """
        if self.price_history is None or self.price_history.empty:
            return
        
        self._calculate_signals()
    
    def _calculate_signals(self) -> None:
        """
        计算所有交易信号
        - 使用RollingOLS实现滚动回归
        - 基于回归的beta和beta标准误差计算交易区间
        - 考虑交易手续费和滑点的影响
        - 向量化计算以提高效率
        """
        window = self.params['window']
        std_dev_mult = self.params['std_dev_mult']
        commission_rate = self.params['commission_rate']
        slippage_rate = self.params['slippage_rate']
        
        # 计算价格比率
        price_ratio = self.price_history[self.fund1] / self.price_history[self.fund2]
        
        # 准备回归数据
        y = self.price_history[self.fund1]
        X = self.price_history[self.fund2]
        
        try:
            # 执行滚动回归，指定窗口大小
            rolling_model = RollingOLS(y, X, window=window)
            rolling_results = rolling_model.fit()
            
            # 获取滚动回归的beta和标准误差
            beta_values = rolling_results.params[self.fund2].shift(1)
            beta_std_values = rolling_results.bse[self.fund2].shift(1)
            
            # 计算交易区间
            upper_values = beta_values + std_dev_mult * beta_std_values
            lower_values = beta_values - std_dev_mult * beta_std_values
            
            # 计算考虑手续费和滑点的交易边界
            # 买入fund1/卖出fund2的边界 (下界): 需要价格比率更低才能覆盖成本
            trading_lower = lower_values / ((1 + commission_rate) * (1 + slippage_rate))**4
            # 卖出fund1/买入fund2的边界 (上界): 需要价格比率更高才能覆盖成本
            trading_upper = upper_values * ((1 + commission_rate) * (1 + slippage_rate))**4
            
            # 创建信号DataFrame
            self.signals_df = pd.DataFrame({
                'ma': beta_values,  # 使用滚动beta作为均值
                'std': beta_std_values,  # 使用滚动beta标准误差
                'upper_bound': trading_upper,  # 实际交易用的上边界（含手续费）
                'lower_bound': trading_lower,  # 实际交易用的下边界（含手续费）
                'raw_upper': upper_values,     # 原始上边界（不含手续费）
                'raw_lower': lower_values,     # 原始下边界（不含手续费）
                'fund2_price': self.price_history[self.fund2],
                'fund1_price': self.price_history[self.fund1],
                'price_ratio': price_ratio,
                'beta': beta_values  # 保存beta值
            }, index=self.price_history.index)
            
            # 预计算交易信号
            # 当价格比率 > 上限时：卖出fund1, 买入fund2 (卖高买低) -> 信号为-1
            # 当价格比率 < 下限时：买入fund1, 卖出fund2 (买低卖高) -> 信号为1
            self.signals_df['trade_signal'] = np.select(
                [
                    self.signals_df['price_ratio'] > self.signals_df['upper_bound'],
                    self.signals_df['price_ratio'] < self.signals_df['lower_bound']
                ],
                [-1, 1],  # -1表示卖fund1买fund2，1表示买fund1卖fund2
                default=0  # 默认不交易
            )
            
            logger.debug(f"计算交易信号完成，数据长度: {len(self.signals_df)}")
            
        except Exception as e:
            logger.error(f"计算交易信号失败: {e}")
            # 创建空的信号DataFrame
            self.signals_df = pd.DataFrame(index=self.price_history.index)
            self.signals_df['trade_signal'] = 0
    
    def generate_signals(self) -> pd.Series:
        """
        生成交易信号
        
        Returns:
            交易信号Series：1为买入fund1，-1为买入fund2，0为持平
        """
        if self.signals_df is None or self.signals_df.empty:
            return pd.Series(0, index=self.data.index)
        
        # 将信号对齐到主数据的索引
        signals = pd.Series(0, index=self.data.index)
        
        # 找到共同的索引
        common_index = signals.index.intersection(self.signals_df.index)
        
        if len(common_index) > 0:
            signals.loc[common_index] = self.signals_df.loc[common_index, 'trade_signal']
        
        return signals
    
    def calculate_positions(self, signals: pd.Series) -> pd.Series:
        """
        根据信号计算仓位
        
        Args:
            signals: 交易信号Series
            
        Returns:
            仓位Series，表示fund1的仓位（正数为多头，负数表示持有fund2）
        """
        position_size = self.params['position_size']
        
        # 初始化仓位
        positions = pd.Series(0.0, index=signals.index)
        current_position = 0.0
        
        for i, signal in enumerate(signals):
            if signal == 1:  # 买入fund1信号
                current_position = position_size
            elif signal == -1:  # 买入fund2信号（卖出fund1）
                current_position = -position_size
            # signal == 0时保持当前仓位
            
            positions.iloc[i] = current_position
        
        return positions
    
    def get_trading_summary(self) -> Dict[str, Any]:
        """
        获取交易摘要信息
        
        Returns:
            包含交易统计的字典
        """
        if self.signals_df is None or self.signals_df.empty:
            return {}
        
        signals = self.signals_df['trade_signal']
        
        return {
            'total_signals': len(signals[signals != 0]),
            'buy_fund1_signals': len(signals[signals == 1]),
            'buy_fund2_signals': len(signals[signals == -1]),
            'signal_ratio': len(signals[signals != 0]) / len(signals) if len(signals) > 0 else 0,
            'avg_price_ratio': self.signals_df['price_ratio'].mean(),
            'price_ratio_std': self.signals_df['price_ratio'].std(),
            'avg_beta': self.signals_df['beta'].mean(),
            'beta_std': self.signals_df['beta'].std()
        }
    
    def get_signals_dataframe(self) -> Optional[pd.DataFrame]:
        """
        获取完整的信号DataFrame，用于分析和调试
        
        Returns:
            信号DataFrame或None
        """
        return self.signals_df.copy() if self.signals_df is not None else None
