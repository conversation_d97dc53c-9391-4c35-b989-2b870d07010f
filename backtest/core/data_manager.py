"""
数据管理器模块 - 负责数据的加载、预处理和管理
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Union, Optional, Any, Tuple
import os
import datetime
from functools import lru_cache
import yfinance as yf
import joblib
from concurrent.futures import ThreadPoolExecutor
import sqlite3
import hashlib
import logging
from pathlib import Path

from ..data.pytdx_client import TdxDataClient

logger = logging.getLogger(__name__)


class DataManager:
    """
    数据管理器

    负责加载、预处理和管理回测所需的市场数据。
    支持从多种来源加载数据，包括CSV文件、数据库和在线API。
    提供数据缓存和并行处理功能，优化性能。
    支持pytdx数据源和智能本地缓存。
    """

    # 频率转换映射
    FREQUENCY_CONVERSION = {
        '1min': {'target': '5min', 'factor': 5},
        '5min': {'target': '15min', 'factor': 3},
        '15min': {'target': '30min', 'factor': 2},
        '30min': {'target': '1h', 'factor': 2},
        '1h': {'target': '1d', 'factor': 4},  # 假设4小时交易
        '1d': {'target': '1w', 'factor': 5},
        '1w': {'target': '1M', 'factor': 4}
    }

    def __init__(self,
                 cache_dir: Optional[str] = None,
                 use_local_cache: bool = True,
                 pytdx_servers: Optional[List[Tuple[str, int]]] = None,
                 auto_select_server: bool = True):
        """
        初始化数据管理器

        Args:
            cache_dir: 数据缓存目录，如果为None则使用默认目录
            use_local_cache: 是否使用本地数据缓存
            pytdx_servers: pytdx服务器列表
            auto_select_server: 是否自动选择最优服务器
        """
        self.cache_dir = cache_dir or "data_cache"
        self.use_local_cache = use_local_cache

        if self.cache_dir and not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

        self.data_cache = {}  # 内存数据缓存

        # 初始化本地数据库
        if use_local_cache:
            self.db_path = os.path.join(self.cache_dir, "market_data.db")
            self._init_local_database()

        # 初始化pytdx客户端
        self.tdx_client = TdxDataClient(
            servers=pytdx_servers,
            auto_select_server=auto_select_server
        )

    def _init_local_database(self) -> None:
        """初始化本地SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建市场数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_data (
                symbol TEXT,
                frequency TEXT,
                datetime TEXT,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume REAL,
                amount REAL,
                PRIMARY KEY (symbol, frequency, datetime)
            )
        ''')

        # 创建索引
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_symbol_freq_date
            ON market_data (symbol, frequency, datetime)
        ''')

        conn.commit()
        conn.close()

    def _save_to_local_db(self, symbol: str, frequency: str, data: pd.DataFrame) -> None:
        """保存数据到本地数据库"""
        if not self.use_local_cache or data.empty:
            return

        conn = sqlite3.connect(self.db_path)

        # 准备数据
        data_to_save = data.copy()
        data_to_save['symbol'] = symbol
        data_to_save['frequency'] = frequency
        data_to_save['datetime'] = data_to_save.index.astype(str)

        # 重新排列列顺序
        columns = ['symbol', 'frequency', 'datetime', 'open', 'high', 'low', 'close', 'volume']
        if 'amount' in data_to_save.columns:
            columns.append('amount')

        data_to_save = data_to_save[columns]

        # 使用INSERT OR REPLACE避免重复数据
        try:
            data_to_save.to_sql('market_data', conn, if_exists='append', index=False, method='multi')
        except Exception as e:
            # 如果有重复数据，先删除再插入
            logger.debug(f"数据插入冲突，尝试更新: {e}")
            cursor = conn.cursor()
            for _, row in data_to_save.iterrows():
                cursor.execute('''
                    INSERT OR REPLACE INTO market_data
                    (symbol, frequency, datetime, open, high, low, close, volume, amount)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', tuple(row))
            conn.commit()

        conn.close()
        logger.debug(f"保存 {len(data)} 条数据到本地数据库: {symbol} {frequency}")

    def _load_from_local_db(self,
                           symbol: str,
                           frequency: str,
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """从本地数据库加载数据"""
        if not self.use_local_cache or not os.path.exists(self.db_path):
            return None

        conn = sqlite3.connect(self.db_path)

        # 构建查询SQL
        query = "SELECT * FROM market_data WHERE symbol = ? AND frequency = ?"
        params = [symbol, frequency]

        if start_date:
            query += " AND datetime >= ?"
            params.append(start_date)

        if end_date:
            query += " AND datetime <= ?"
            params.append(end_date)

        query += " ORDER BY datetime"

        try:
            df = pd.read_sql_query(query, conn, params=tuple(params))
            conn.close()

            if df.empty:
                return None

            # 处理数据格式
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            df.drop(['symbol', 'frequency'], axis=1, inplace=True)

            logger.debug(f"从本地数据库加载 {len(df)} 条数据: {symbol} {frequency}")
            return df

        except Exception as e:
            logger.error(f"从本地数据库加载数据失败: {e}")
            conn.close()
            return None

    def load_csv(self, filepath: str, date_col: str = 'date',
                 datetime_format: Optional[str] = None, resample: Optional[str] = None) -> pd.DataFrame:
        """
        从CSV文件加载数据
        
        Args:
            filepath: CSV文件路径
            date_col: 日期列名
            datetime_format: 日期格式字符串，如'%Y-%m-%d'
            resample: 重采样频率，如'1D'、'1H'等
            
        Returns:
            加载并处理后的DataFrame
        """
        # 检查缓存
        cache_key = f"csv_{filepath}_{resample}"
        if cache_key in self.data_cache:
            return self.data_cache[cache_key]
        
        # 加载数据
        df = pd.read_csv(filepath)
        
        # 处理日期列
        if datetime_format:
            df[date_col] = pd.to_datetime(df[date_col], format=datetime_format)
        else:
            df[date_col] = pd.to_datetime(df[date_col])
        
        # 设置索引
        df.set_index(date_col, inplace=True)
        df.sort_index(inplace=True)
        
        # 重采样
        if resample:
            df = self._resample_data(df, resample)
        
        # 缓存数据
        self.data_cache[cache_key] = df
        
        return df

    def load_pytdx(self,
                   symbols: Union[str, List[str]],
                   start_date: str,
                   end_date: str,
                   frequency: str = '1d',
                   fields: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
        """
        从pytdx加载数据，支持智能本地缓存

        Args:
            symbols: 单个股票代码或股票代码列表
            start_date: 开始日期，格式为'YYYY-MM-DD'
            end_date: 结束日期，格式为'YYYY-MM-DD'
            frequency: 数据频率，如'1min', '5min', '1d'等
            fields: 需要的字段列表，如['open', 'close', 'high', 'low', 'volume']

        Returns:
            字典，键为股票代码，值为对应的DataFrame
        """
        if isinstance(symbols, str):
            symbols = [symbols]

        result = {}

        for symbol in symbols:
            # 首先尝试从本地缓存加载
            local_data = self._load_from_local_db(symbol, frequency, start_date, end_date)

            if local_data is not None and self._is_data_complete(local_data, start_date, end_date, frequency):
                logger.info(f"从本地缓存加载数据: {symbol} {frequency}")
                result[symbol] = local_data
            else:
                # 尝试频率转换
                converted_data = self._try_frequency_conversion(symbol, frequency, start_date, end_date)

                if converted_data is not None:
                    logger.info(f"通过频率转换获取数据: {symbol} {frequency}")
                    result[symbol] = converted_data
                    # 保存转换后的数据到本地
                    self._save_to_local_db(symbol, frequency, converted_data)
                else:
                    # 从pytdx获取数据
                    logger.info(f"从pytdx获取数据: {symbol} {frequency}")
                    pytdx_data = self.tdx_client.get_security_bars(
                        symbol, frequency, start_date, end_date
                    )

                    if pytdx_data is not None and not pytdx_data.empty:
                        # 保存到本地数据库
                        self._save_to_local_db(symbol, frequency, pytdx_data)

                        # 如果有本地数据，合并增量数据
                        if local_data is not None:
                            pytdx_data = self._merge_incremental_data(local_data, pytdx_data)
                            self._save_to_local_db(symbol, frequency, pytdx_data)

                        result[symbol] = pytdx_data
                    else:
                        logger.warning(f"无法获取数据: {symbol} {frequency}")
                        result[symbol] = pd.DataFrame()

            # 过滤字段
            if fields and symbol in result and not result[symbol].empty:
                available_fields = [f for f in fields if f in result[symbol].columns]
                result[symbol] = result[symbol][available_fields]

        return result

    def _is_data_complete(self, data: pd.DataFrame, start_date: str, end_date: str, frequency: str) -> bool:
        """检查本地数据是否完整"""
        if data.empty:
            return False

        start_ts = pd.Timestamp(start_date)
        end_ts = pd.Timestamp(end_date)

        # 检查日期范围
        data_start = data.index.min()
        data_end = data.index.max()

        # 允许一定的容差
        if frequency == '1d':
            tolerance = pd.Timedelta(days=3)  # 考虑周末和节假日
        elif frequency in ['1h', '30min', '15min', '5min']:
            tolerance = pd.Timedelta(days=1)
        else:
            tolerance = pd.Timedelta(hours=1)

        return (data_start <= start_ts + tolerance) and (data_end >= end_ts - tolerance)

    def _try_frequency_conversion(self, symbol: str, target_freq: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """尝试通过频率转换获取数据"""
        # 查找可以转换的更高频率数据
        for source_freq, conversion_info in self.FREQUENCY_CONVERSION.items():
            if conversion_info['target'] == target_freq:
                # 尝试从更高频率数据转换
                source_data = self._load_from_local_db(symbol, source_freq, start_date, end_date)
                if source_data is not None and not source_data.empty:
                    return self._convert_frequency(source_data, source_freq, target_freq)

        return None

    def _convert_frequency(self, data: pd.DataFrame, source_freq: str, target_freq: str) -> pd.DataFrame:
        """频率转换"""
        if source_freq == target_freq:
            return data

        # 定义重采样规则
        resample_rules = {
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }

        # 执行重采样
        resampled = data.resample(target_freq).agg({
            col: resample_rules.get(col, 'last')
            for col in data.columns if col in resample_rules
        })

        # 移除空值行
        resampled = resampled.dropna()

        return resampled

    def _merge_incremental_data(self, old_data: pd.DataFrame, new_data: pd.DataFrame) -> pd.DataFrame:
        """合并增量数据"""
        # 合并数据，新数据优先
        combined = pd.concat([old_data, new_data])

        # 去重，保留最新的数据
        combined = combined[~combined.index.duplicated(keep='last')]

        # 排序
        combined = combined.sort_index()

        return combined

    @lru_cache(maxsize=32)
    def load_yahoo(self, symbols: Union[str, List[str]], start_date: str, 
                  end_date: str, interval: str = '1d') -> Dict[str, pd.DataFrame]:
        """
        从Yahoo Finance加载数据
        
        Args:
            symbols: 单个股票代码或股票代码列表
            start_date: 开始日期，格式为'YYYY-MM-DD'
            end_date: 结束日期，格式为'YYYY-MM-DD'
            interval: 数据间隔，如'1d'、'1h'等
            
        Returns:
            字典，键为股票代码，值为对应的DataFrame
        """
        # 转换为列表
        if isinstance(symbols, str):
            symbols = [symbols]
        
        # 检查缓存
        if self.cache_dir:
            cache_file = os.path.join(
                self.cache_dir, 
                f"yahoo_{'-'.join(symbols)}_{start_date}_{end_date}_{interval}.pkl"
            )
            if os.path.exists(cache_file):
                return joblib.load(cache_file)
        
        # 并行下载数据
        with ThreadPoolExecutor(max_workers=min(10, len(symbols))) as executor:
            results = list(executor.map(
                lambda symbol: (symbol, yf.download(
                    symbol, start=start_date, end=end_date, interval=interval
                )),
                symbols
            ))
        
        # 整理结果
        data_dict = {symbol: df for symbol, df in results}
        
        # 缓存结果
        if self.cache_dir:
            joblib.dump(data_dict, cache_file)
        
        return data_dict
    
    def preprocess_data(self, data: pd.DataFrame, fill_method: str = 'ffill',
                       add_features: bool = True) -> pd.DataFrame:
        """
        预处理数据
        
        Args:
            data: 原始数据DataFrame
            fill_method: 填充缺失值的方法，如'ffill'、'bfill'等
            add_features: 是否添加常用特征
            
        Returns:
            预处理后的DataFrame
        """
        # 复制数据，避免修改原始数据
        df = data.copy()
        
        # 处理缺失值
        if fill_method:
            if fill_method == 'ffill':
                df = df.ffill()
            elif fill_method == 'bfill':
                df = df.bfill()
            else:
                df = df.ffill()
        
        # 确保OHLCV列存在
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                if col == 'volume' and 'Volume' in df.columns:
                    df['volume'] = df['Volume']
                elif col == 'close' and 'Close' in df.columns:
                    df['close'] = df['Close']
                elif col == 'open' and 'Open' in df.columns:
                    df['open'] = df['Open']
                elif col == 'high' and 'High' in df.columns:
                    df['high'] = df['High']
                elif col == 'low' and 'Low' in df.columns:
                    df['low'] = df['Low']
        
        # 添加常用特征
        if add_features:
            # 计算收益率
            df['returns'] = df['close'].pct_change()
            
            # 计算波动率（20日滚动标准差）
            df['volatility'] = df['returns'].rolling(window=20).std()
            
            # 计算移动平均线
            df['sma_5'] = df['close'].rolling(window=5).mean()
            df['sma_10'] = df['close'].rolling(window=10).mean()
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['sma_60'] = df['close'].rolling(window=60).mean()
            
            # 计算交易量变化
            df['volume_change'] = df['volume'].pct_change()
        
        return df
    
    def _resample_data(self, df: pd.DataFrame, freq: str) -> pd.DataFrame:
        """
        重采样数据
        
        Args:
            df: 原始数据DataFrame
            freq: 重采样频率，如'1D'、'1H'等
            
        Returns:
            重采样后的DataFrame
        """
        # 确保索引是日期时间类型
        if not isinstance(df.index, pd.DatetimeIndex):
            raise ValueError("DataFrame索引必须是DatetimeIndex类型才能进行重采样")
        
        # 定义OHLCV重采样规则
        ohlcv_dict = {
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }
        
        # 筛选出存在的OHLCV列
        resample_dict = {k: v for k, v in ohlcv_dict.items() if k in df.columns}
        
        # 对其他列使用'last'规则
        for col in df.columns:
            if col not in resample_dict:
                resample_dict[col] = 'last'
        
        # 执行重采样
        resampled = df.resample(freq).agg(resample_dict)
        
        return resampled
    
    def split_data(self, data: pd.DataFrame, train_ratio: float = 0.7, 
                  valid_ratio: float = 0.15) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        将数据分割为训练集、验证集和测试集
        
        Args:
            data: 原始数据DataFrame
            train_ratio: 训练集比例
            valid_ratio: 验证集比例
            
        Returns:
            (train_data, valid_data, test_data)元组
        """
        # 计算分割点
        n = len(data)
        train_end = int(n * train_ratio)
        valid_end = train_end + int(n * valid_ratio)
        
        # 分割数据
        train_data = data.iloc[:train_end].copy()
        valid_data = data.iloc[train_end:valid_end].copy()
        test_data = data.iloc[valid_end:].copy()
        
        return train_data, valid_data, test_data
    
    def clear_cache(self) -> None:
        """清除所有缓存"""
        self.data_cache.clear()
        if self.cache_dir and os.path.exists(self.cache_dir):
            for file in os.listdir(self.cache_dir):
                if file.endswith('.pkl'):
                    os.remove(os.path.join(self.cache_dir, file))
