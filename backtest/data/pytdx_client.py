"""
pytdx数据客户端 - 基于pytdx库的数据获取接口
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Union, Optional, Any, Tuple
import time
import logging
from datetime import datetime, timedelta
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from pytdx.hq import TdxHq_API
from pytdx.params import TDXParams
PYTDX_AVAILABLE = True


logger = logging.getLogger(__name__)


class TdxDataClient:
    """
    通达信数据客户端
    
    提供基于pytdx的数据获取功能，支持最优服务器自动筛选和手动配置。
    """
    
    # 默认服务器列表
    DEFAULT_SERVERS = [
        ('119.147.212.81', 7709),  # 通达信1
        ('124.74.236.94', 7709),   # 通达信2
        ('60.12.136.250', 7709),   # 通达信3
        ('119.147.164.60', 7709),  # 通达信4
        ('61.153.144.179', 7709),  # 通达信5
        ('140.207.202.181', 7709), # 通达信6
        ('47.103.48.45', 7709),    # 通达信7
        ('47.103.86.229', 7709),   # 通达信8
        ('111.235.157.13', 7709),  # 内蒙
        ('123.125.108.219', 7709),   # 北京联通
        ('103.251.85.180', 7709),   # 上海移动
        ('58.63.254.155', 7709), # 广州电信
    ]

    # 频率映射
    FREQUENCY_MAP = {
        '1min': 8,    # 1分钟
        '5min': 0,    # 5分钟
        '15min': 1,   # 15分钟
        '30min': 2,   # 30分钟
        '1h': 3,      # 1小时
        '1d': 9,      # 日线
        '1w': 5,      # 周线
        '1M': 6,      # 月线
    }
    
    def __init__(self, 
                 servers: Optional[List[Tuple[str, int]]] = None,
                 auto_select_server: bool = True,
                 timeout: float = 5.0):
        """
        初始化TDX数据客户端
        
        Args:
            servers: 服务器列表，格式为[(host, port), ...]
            auto_select_server: 是否自动选择最优服务器
            timeout: 连接超时时间
        """
        if not PYTDX_AVAILABLE:
            raise ImportError("pytdx库未安装，请运行: pip install pytdx")
        
        self.servers = servers or self.DEFAULT_SERVERS
        self.auto_select_server = auto_select_server
        self.timeout = timeout
        self.best_server = None
        self.api = None
        self._lock = threading.Lock()
        
        # 如果启用自动选择，则测试服务器
        if auto_select_server:
            self.best_server = self._select_best_server()
        else:
            self.best_server = self.servers[0]
    
    def _test_server_speed(self, server: Tuple[str, int]) -> Optional[float]:
        """
        测试服务器响应速度
        
        Args:
            server: 服务器地址和端口
            
        Returns:
            响应时间（秒），如果连接失败返回None
        """
        host, port = server
        api = TdxHq_API()
        
        try:
            start_time = time.time()
            if api.connect(host, port, time_out=self.timeout):
                # 测试获取股票数量
                result = api.get_security_count(0)  # 测试深圳市场
                if result and result > 0:
                    response_time = time.time() - start_time
                    api.disconnect()
                    return response_time
            api.disconnect()
            return None
        except Exception as e:
            logger.debug(f"测试服务器 {host}:{port} 失败: {e}")
            try:
                api.disconnect()
            except:
                pass
            return None
    
    def _select_best_server(self) -> Tuple[str, int]:
        """
        选择最优服务器
        
        Returns:
            最优服务器的(host, port)元组
        """
        logger.info("正在测试服务器速度...")
        
        with ThreadPoolExecutor(max_workers=len(self.servers)) as executor:
            # 提交所有服务器测试任务
            future_to_server = {
                executor.submit(self._test_server_speed, server): server 
                for server in self.servers
            }
            
            server_speeds = []
            for future in as_completed(future_to_server):
                server = future_to_server[future]
                try:
                    speed = future.result()
                    if speed is not None:
                        server_speeds.append((server, speed))
                        logger.debug(f"服务器 {server[0]}:{server[1]} 响应时间: {speed:.3f}秒")
                except Exception as e:
                    logger.debug(f"测试服务器 {server[0]}:{server[1]} 异常: {e}")
        
        if not server_speeds:
            logger.warning("所有服务器测试失败，使用默认服务器")
            return self.servers[0]
        
        # 选择响应时间最短的服务器
        best_server, best_speed = min(server_speeds, key=lambda x: x[1])
        logger.info(f"选择最优服务器: {best_server[0]}:{best_server[1]} (响应时间: {best_speed:.3f}秒)")
        
        return best_server
    
    def connect(self) -> bool:
        """
        连接到服务器
        
        Returns:
            连接是否成功
        """
        with self._lock:
            if self.api is not None:
                try:
                    self.api.disconnect()
                except:
                    pass
            
            self.api = TdxHq_API()
            host, port = self.best_server
            
            try:
                success = self.api.connect(host, port, time_out=self.timeout)
                if success:
                    logger.debug(f"成功连接到服务器 {host}:{port}")
                else:
                    logger.error(f"连接服务器失败 {host}:{port}")
                return success
            except Exception as e:
                logger.error(f"连接服务器异常 {host}:{port}: {e}")
                return False
    
    def disconnect(self) -> None:
        """断开连接"""
        with self._lock:
            if self.api is not None:
                try:
                    self.api.disconnect()
                except:
                    pass
                self.api = None
    
    def get_security_bars(self,
                         symbol: str,
                         frequency: str = '1d',
                         start_date: Optional[str] = None,
                         end_date: Optional[str] = None,
                         count: int = 800) -> pd.DataFrame:
        """
        获取K线数据

        Args:
            symbol: 股票代码，如'000001'
            frequency: 数据频率，支持'1min', '5min', '15min', '30min', '1h', '1d', '1w', '1M'
            start_date: 开始日期，格式'YYYY-MM-DD'
            end_date: 结束日期，格式'YYYY-MM-DD'
            count: 获取数据条数

        Returns:
            包含OHLCV数据的DataFrame
        """
        if not self.connect():
            raise ConnectionError("无法连接到TDX服务器")

        try:
            # 确定市场代码
            market = self._get_market_code(symbol)

            # 获取频率代码
            if frequency not in self.FREQUENCY_MAP:
                raise ValueError(f"不支持的频率: {frequency}")
            freq_code = self.FREQUENCY_MAP[frequency]

            # 如果指定了日期范围，需要估算需要获取的数据量
            if start_date and end_date:
                start_ts = pd.Timestamp(start_date)
                end_ts = pd.Timestamp(end_date)

                # 根据频率估算需要的数据量
                if frequency == '1min':
                    # 每天约240分钟交易时间
                    days = (end_ts - start_ts).days + 1
                    estimated_count = days * 240
                elif frequency == '5min':
                    days = (end_ts - start_ts).days + 1
                    estimated_count = days * 48
                elif frequency == '15min':
                    days = (end_ts - start_ts).days + 1
                    estimated_count = days * 16
                elif frequency == '30min':
                    days = (end_ts - start_ts).days + 1
                    estimated_count = days * 8
                elif frequency == '1h':
                    days = (end_ts - start_ts).days + 1
                    estimated_count = days * 4
                elif frequency == '1d':
                    estimated_count = (end_ts - start_ts).days + 1
                elif frequency == '1w':
                    estimated_count = ((end_ts - start_ts).days // 7) + 1
                elif frequency == '1M':
                    estimated_count = ((end_ts - start_ts).days // 30) + 1
                else:
                    estimated_count = count

                # 为了确保获取到足够的数据，增加一些缓冲
                count = min(max(estimated_count * 2, count), 10000)

            # 分批获取数据
            all_data = []
            pos = 0
            max_batch_size = 800

            while len(all_data) < count:
                batch_count = min(max_batch_size, count - len(all_data))

                try:
                    result = self.api.get_security_bars(
                        freq_code, market, symbol, pos, batch_count
                    )

                    if not result:
                        logger.debug(f"没有更多数据，pos={pos}, batch_count={batch_count}")
                        break

                    all_data.extend(result)
                    pos += len(result)

                    logger.debug(f"获取到 {len(result)} 条数据，总计 {len(all_data)} 条")

                    # 如果返回的数据少于请求的数量，说明已经到头了
                    if len(result) < batch_count:
                        logger.debug(f"数据获取完毕，返回数据量 {len(result)} < 请求量 {batch_count}")
                        break

                    # 如果指定了日期范围，检查是否已经获取到足够早的数据
                    if start_date and all_data:
                        earliest_data = min(all_data, key=lambda x: x['datetime'])
                        earliest_date = pd.Timestamp(earliest_data['datetime'])
                        if earliest_date <= pd.Timestamp(start_date):
                            logger.debug(f"已获取到开始日期 {start_date} 的数据")
                            break

                except Exception as e:
                    logger.error(f"获取数据失败: {e}")
                    break

            if not all_data:
                logger.warning(f"未获取到任何数据: {symbol} {frequency}")
                return pd.DataFrame()

            # 转换为DataFrame
            df = pd.DataFrame(all_data)

            # 处理日期时间
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            df.sort_index(inplace=True)

            # 重命名列
            column_mapping = {
                'open': 'open',
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'vol': 'volume',
                'amount': 'amount'
            }

            df = df.rename(columns=column_mapping)

            # 确保数据类型正确
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 过滤日期范围
            if start_date:
                start_ts = pd.Timestamp(start_date)
                df = df[df.index >= start_ts]

            if end_date:
                end_ts = pd.Timestamp(end_date)
                df = df[df.index <= end_ts]

            logger.info(f"成功获取 {symbol} {frequency} 数据: {len(df)} 条记录")
            if len(df) > 0:
                logger.info(f"数据时间范围: {df.index.min()} 至 {df.index.max()}")

            return df

        finally:
            self.disconnect()
    
    def _get_market_code(self, symbol: str) -> int:
        """
        根据股票代码确定市场代码

        Args:
            symbol: 股票代码

        Returns:
            市场代码，0为深圳，1为上海
        """
        # 股票和基金代码判断逻辑
        if symbol.startswith(('000', '002', '003', '300')):
            return 0  # 深圳股票
        elif symbol.startswith(('600', '601', '603', '605', '688')):
            return 1  # 上海股票
        elif symbol.startswith(('159', '150', '160', '161', '162', '163', '164', '165', '166', '167', '168', '169')):
            return 0  # 深圳基金/ETF
        elif symbol.startswith(('510', '511', '512', '513', '514', '515', '516', '517', '518', '519')):
            return 1  # 上海基金/ETF
        else:
            # 对于其他代码，尝试两个市场
            logger.debug(f"未知代码格式 {symbol}，默认使用深圳市场")
            return 0
    
    def get_stock_list(self, market: int = 0) -> List[Dict[str, Any]]:
        """
        获取股票列表
        
        Args:
            market: 市场代码，0为深圳，1为上海
            
        Returns:
            股票信息列表
        """
        if not self.connect():
            raise ConnectionError("无法连接到TDX服务器")
        
        try:
            stocks = []
            start = 0
            
            while True:
                result = self.api.get_security_list(market, start)
                if not result:
                    break
                
                stocks.extend(result)
                start += len(result)
                
                # 如果返回的数据少于1000，说明已经到头了
                if len(result) < 1000:
                    break
            
            return stocks
            
        finally:
            self.disconnect()
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
