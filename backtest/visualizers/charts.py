"""
图表生成模块 - 创建标准化的可视化图表
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, List, Union, Optional, Any, Tuple
import os
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体支持
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置默认样式
try:
    plt.style.use('seaborn-v0_8')
except:
    plt.style.use('seaborn')
sns.set_palette("husl")


class ChartGenerator:
    """
    图表生成器
    
    创建标准化的可视化图表，支持中文显示和统一风格。
    """
    
    def __init__(self, 
                 figsize: Tuple[int, int] = (12, 8),
                 dpi: int = 100,
                 style: str = 'seaborn-v0_8'):
        """
        初始化图表生成器
        
        Args:
            figsize: 图表尺寸
            dpi: 图表分辨率
            style: 图表样式
        """
        self.figsize = figsize
        self.dpi = dpi
        self.style = style
        
        # 设置样式
        plt.style.use(style)
        
        # 定义颜色方案
        self.colors = {
            'primary': '#1f77b4',
            'secondary': '#ff7f0e', 
            'success': '#2ca02c',
            'danger': '#d62728',
            'warning': '#ff7f0e',
            'info': '#17a2b8',
            'light': '#f8f9fa',
            'dark': '#343a40'
        }
    
    def plot_equity_curve(self, 
                         equity_curve: pd.DataFrame,
                         benchmark: Optional[pd.Series] = None,
                         save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制权益曲线图
        
        Args:
            equity_curve: 权益曲线DataFrame
            benchmark: 基准收益Series（可选）
            save_path: 保存路径（可选）
            
        Returns:
            matplotlib Figure对象
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figsize, 
                                      height_ratios=[3, 1], dpi=self.dpi)
        
        # 绘制权益曲线
        ax1.plot(equity_curve.index, equity_curve['equity'], 
                label='策略权益', color=self.colors['primary'], linewidth=2)
        
        # 如果有基准，也绘制基准曲线
        if benchmark is not None:
            ax1.plot(benchmark.index, benchmark.values, 
                    label='基准', color=self.colors['secondary'], linewidth=1.5)
        
        ax1.set_title('权益曲线', fontsize=16, fontweight='bold')
        ax1.set_ylabel('权益价值', fontsize=12)
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)
        
        # 绘制回撤图
        if 'drawdown' in equity_curve.columns:
            ax2.fill_between(equity_curve.index, equity_curve['drawdown'], 0,
                           color=self.colors['danger'], alpha=0.3, label='回撤')
            ax2.plot(equity_curve.index, equity_curve['drawdown'],
                    color=self.colors['danger'], linewidth=1)
        
        ax2.set_title('回撤分析', fontsize=14)
        ax2.set_ylabel('回撤比例', fontsize=12)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)
        
        # 格式化x轴日期
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        return fig
    
    def plot_returns_distribution(self, 
                                 returns: pd.Series,
                                 save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制收益率分布图
        
        Args:
            returns: 收益率Series
            save_path: 保存路径（可选）
            
        Returns:
            matplotlib Figure对象
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figsize, dpi=self.dpi)
        
        # 收益率直方图
        ax1.hist(returns.dropna(), bins=50, alpha=0.7, color=self.colors['primary'], edgecolor='black')
        ax1.axvline(returns.mean(), color=self.colors['danger'], linestyle='--', 
                   label=f'均值: {returns.mean():.4f}')
        ax1.set_title('收益率分布', fontsize=14)
        ax1.set_xlabel('日收益率', fontsize=12)
        ax1.set_ylabel('频次', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Q-Q图
        from scipy import stats
        stats.probplot(returns.dropna(), dist="norm", plot=ax2)
        ax2.set_title('Q-Q图（正态性检验）', fontsize=14)
        ax2.grid(True, alpha=0.3)
        
        # 滚动收益率
        rolling_returns = returns.rolling(window=20).mean()
        ax3.plot(rolling_returns.index, rolling_returns, color=self.colors['success'], linewidth=1.5)
        ax3.set_title('20日滚动平均收益率', fontsize=14)
        ax3.set_xlabel('日期', fontsize=12)
        ax3.set_ylabel('收益率', fontsize=12)
        ax3.grid(True, alpha=0.3)
        
        # 滚动波动率
        rolling_vol = returns.rolling(window=20).std() * np.sqrt(252)
        ax4.plot(rolling_vol.index, rolling_vol, color=self.colors['warning'], linewidth=1.5)
        ax4.set_title('20日滚动年化波动率', fontsize=14)
        ax4.set_xlabel('日期', fontsize=12)
        ax4.set_ylabel('波动率', fontsize=12)
        ax4.grid(True, alpha=0.3)
        
        # 格式化日期轴
        for ax in [ax3, ax4]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        return fig
    
    def plot_monthly_returns_heatmap(self, 
                                   returns: pd.Series,
                                   save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制月度收益热图
        
        Args:
            returns: 收益率Series
            save_path: 保存路径（可选）
            
        Returns:
            matplotlib Figure对象
        """
        # 计算月度收益
        monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
        
        # 创建年月矩阵
        monthly_returns_df = monthly_returns.to_frame('returns')
        monthly_returns_df['year'] = monthly_returns_df.index.year
        monthly_returns_df['month'] = monthly_returns_df.index.month
        
        # 透视表
        heatmap_data = monthly_returns_df.pivot(index='year', columns='month', values='returns')
        
        # 设置月份标签
        month_labels = ['1月', '2月', '3月', '4月', '5月', '6月',
                       '7月', '8月', '9月', '10月', '11月', '12月']
        
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        
        # 绘制热图
        sns.heatmap(heatmap_data, annot=True, fmt='.2%', cmap='RdYlGn', center=0,
                   xticklabels=month_labels, yticklabels=True, ax=ax,
                   cbar_kws={'label': '月度收益率'})
        
        ax.set_title('月度收益率热图', fontsize=16, fontweight='bold')
        ax.set_xlabel('月份', fontsize=12)
        ax.set_ylabel('年份', fontsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        return fig
    
    def plot_trade_analysis(self, 
                           trades: pd.DataFrame,
                           save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制交易分析图
        
        Args:
            trades: 交易记录DataFrame
            save_path: 保存路径（可选）
            
        Returns:
            matplotlib Figure对象
        """
        if len(trades) == 0:
            fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
            ax.text(0.5, 0.5, '无交易记录', ha='center', va='center', 
                   transform=ax.transAxes, fontsize=16)
            ax.set_title('交易分析', fontsize=16, fontweight='bold')
            return fig
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figsize, dpi=self.dpi)
        
        # 交易次数统计
        trade_counts = trades['type'].value_counts()
        ax1.pie(trade_counts.values, labels=trade_counts.index, autopct='%1.1f%%',
               colors=[self.colors['success'], self.colors['danger']])
        ax1.set_title('买卖交易分布', fontsize=14)
        
        # 交易价值分布
        ax2.hist(trades['value'], bins=20, alpha=0.7, color=self.colors['info'], edgecolor='black')
        ax2.set_title('交易价值分布', fontsize=14)
        ax2.set_xlabel('交易价值', fontsize=12)
        ax2.set_ylabel('频次', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 交易时间分布
        trades['hour'] = pd.to_datetime(trades['datetime']).dt.hour
        hourly_trades = trades['hour'].value_counts().sort_index()
        ax3.bar(hourly_trades.index, hourly_trades.values, color=self.colors['primary'], alpha=0.7)
        ax3.set_title('交易时间分布', fontsize=14)
        ax3.set_xlabel('小时', fontsize=12)
        ax3.set_ylabel('交易次数', fontsize=12)
        ax3.grid(True, alpha=0.3)
        
        # 累计佣金
        trades_sorted = trades.sort_values('datetime')
        cumulative_commission = trades_sorted['commission'].cumsum()
        ax4.plot(range(len(cumulative_commission)), cumulative_commission, 
                color=self.colors['warning'], linewidth=2)
        ax4.set_title('累计佣金', fontsize=14)
        ax4.set_xlabel('交易序号', fontsize=12)
        ax4.set_ylabel('累计佣金', fontsize=12)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        return fig
